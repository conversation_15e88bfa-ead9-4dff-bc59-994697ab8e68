-- CreateEnum
CREATE TYPE "BrokerClientType" AS ENUM ('ORGANIZATION', 'INDIVIDUAL');

-- CreateEnum
CREATE TYPE "BrokerClientStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'TERMINATED');

-- C<PERSON>Enum
CREATE TYPE "BrokerTransactionType" AS ENUM ('CARBON_CREDIT_SALE', 'CARBON_CREDIT_PURCHASE', 'PROJECT_INVESTMENT', 'CONSULTATION', 'OTHER');

-- CreateEnum
CREATE TYPE "BrokerTransactionStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "BrokerCommissionStatus" AS ENUM ('PENDING', 'CALCULATED', 'APPROVED', 'PAID', 'DISPUTED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "BrokerDocumentType" AS ENUM ('LICENSE', 'CERTIFICATION', 'KYC_DOCUMENT', 'COMPLIANCE_DOCUMENT', 'CONTRACT', 'OTHER');

-- CreateEnum
CREATE TYPE "BrokerDocumentStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "BrokerVerificationStatus" AS ENUM ('PENDING', 'VERIFIED', 'REJECTED', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "BrokerOperatingModel" AS ENUM ('FULL_SERVICE', 'EXECUTION_ONLY', 'ADVISORY_ONLY', 'HYBRID');

-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'ORGANIZATION_ADMIN', 'ORGANIZATION_USER', 'BROKER', 'INDIVIDUAL', 'VERIFIER', 'AUDITOR', 'MARKETPLACE_ADMIN', 'MARKETPLACE_USER', 'SYSTEM', 'SPV_USER');

-- CreateEnum
CREATE TYPE "SPVStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'DISSOLVED');

-- CreateEnum
CREATE TYPE "SPVVerificationStatus" AS ENUM ('PENDING_VERIFICATION', 'IN_REVIEW', 'VERIFIED', 'REJECTED', 'NEEDS_MORE_INFO', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "SPVDocumentType" AS ENUM ('CERTIFICATE_OF_INCORPORATION', 'PAN_CARD', 'GST_REGISTRATION', 'BOARD_RESOLUTION', 'BANK_PROOF', 'AUTHORIZED_SIGNATORY_ID', 'MOA_AOA', 'OTHER');

-- CreateEnum
CREATE TYPE "OrganizationStatus" AS ENUM ('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE');

-- CreateEnum
CREATE TYPE "VerificationStatus" AS ENUM ('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED');

-- CreateEnum
CREATE TYPE "SPVUserRole" AS ENUM ('SITE_WORKER', 'PROJECT_MANAGER', 'SPV_ADMIN');

-- CreateEnum
CREATE TYPE "DataVerificationStatus" AS ENUM ('DRAFT', 'PM_VERIFIED', 'SPV_ADMIN_VERIFIED', 'VERIFIED', 'REJECTED', 'SPV_APPROVED', 'SPV_REJECTED', 'ORG_APPROVED', 'ORG_REJECTED', 'SUBMITTED_TO_VVB', 'VVB_VERIFIED', 'VVB_REJECTED', 'SUBMITTED_FOR_VERIFICATION');

-- CreateEnum
CREATE TYPE "OrganizationSize" AS ENUM ('SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE');

-- CreateEnum
CREATE TYPE "ProjectType" AS ENUM ('RENEWABLE_ENERGY', 'FORESTRY', 'METHANE_REDUCTION', 'ENERGY_EFFICIENCY', 'WASTE_MANAGEMENT', 'AGRICULTURE', 'TRANSPORTATION', 'INDUSTRIAL', 'OTHER');

-- CreateEnum
CREATE TYPE "ProjectStatus" AS ENUM ('PENDING', 'ACTIVE', 'COMPLETED', 'SUSPENDED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ProjectDocumentType" AS ENUM ('PROJECT_DESIGN', 'METHODOLOGY', 'BASELINE_ASSESSMENT', 'MONITORING_PLAN', 'VALIDATION_REPORT', 'VERIFICATION_REPORT', 'LEGAL_DOCUMENT', 'STAKEHOLDER_CONSULTATION', 'ENVIRONMENTAL_IMPACT', 'SOCIAL_IMPACT', 'FINANCIAL_DOCUMENT', 'OTHER');

-- CreateEnum
CREATE TYPE "WalletType" AS ENUM ('GENERAL', 'PROJECT', 'TOKENIZATION', 'TRADING', 'RETIREMENT', 'CUSTODY');

-- CreateEnum
CREATE TYPE "WalletRecoveryType" AS ENUM ('SOCIAL_RECOVERY', 'SEED_PHRASE', 'HARDWARE_BACKUP', 'MULTI_SIG', 'GUARDIAN');

-- CreateEnum
CREATE TYPE "WalletAccessLevel" AS ENUM ('ADMIN', 'MANAGER', 'APPROVER', 'VIEWER');

-- CreateEnum
CREATE TYPE "BridgeStatus" AS ENUM ('PENDING', 'INITIATED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'RECOVERY_NEEDED');

-- CreateEnum
CREATE TYPE "CarbonCreditStatus" AS ENUM ('PENDING', 'VERIFIED', 'LISTED', 'SOLD', 'RETIRED', 'TOKENIZED');

-- CreateEnum
CREATE TYPE "TokenizationStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "TokenStandard" AS ENUM ('ERC20', 'ERC1155', 'CUSTOM');

-- CreateEnum
CREATE TYPE "TokenizationMethod" AS ENUM ('STANDARD', 'BATCH', 'CUSTOM');

-- CreateEnum
CREATE TYPE "MarketplaceStatus" AS ENUM ('PENDING', 'ACTIVE', 'PAUSED', 'SOLD', 'EXPIRED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "PricingStrategy" AS ENUM ('FIXED', 'AUCTION', 'DYNAMIC', 'TIERED');

-- CreateEnum
CREATE TYPE "ListingVisibility" AS ENUM ('PUBLIC', 'PRIVATE', 'INVITE_ONLY');

-- CreateEnum
CREATE TYPE "PriceAlertDirection" AS ENUM ('ABOVE', 'BELOW', 'BOTH');

-- CreateEnum
CREATE TYPE "OrderType" AS ENUM ('BUY', 'SELL');

-- CreateEnum
CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'MATCHED', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('DEPOSIT', 'WITHDRAWAL', 'PURCHASE', 'SALE', 'FEE', 'BRIDGE', 'SWAP', 'TRANSFER', 'RETIREMENT', 'TOKENIZATION');

-- CreateEnum
CREATE TYPE "TransactionStatus" AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REVERTED');

-- CreateEnum
CREATE TYPE "TransactionCategory" AS ENUM ('INCOME', 'EXPENSE', 'INVESTMENT', 'TRADING', 'FEE', 'TAX', 'TRANSFER', 'RETIREMENT', 'TOKENIZATION', 'OTHER');

-- CreateEnum
CREATE TYPE "AuditStatus" AS ENUM ('PENDING', 'VERIFIED', 'FLAGGED', 'RECONCILED', 'NEEDS_REVIEW');

-- CreateEnum
CREATE TYPE "AssetType" AS ENUM ('CARBON_CREDIT', 'TOKEN', 'NFT', 'WALLET', 'PORTFOLIO');

-- CreateEnum
CREATE TYPE "ValuationMethod" AS ENUM ('MARKET_PRICE', 'HISTORICAL_COST', 'FAIR_VALUE', 'DISCOUNTED_CASH_FLOW', 'COMPARABLE_SALES', 'WEIGHTED_AVERAGE', 'MANUAL');

-- CreateEnum
CREATE TYPE "SubscriptionPlan" AS ENUM ('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE');

-- CreateEnum
CREATE TYPE "SubscriptionStatus" AS ENUM ('ACTIVE', 'CANCELLED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('SYSTEM', 'ORDER', 'TRANSACTION', 'CREDIT', 'VERIFICATION', 'SUBSCRIPTION', 'PAYMENT', 'BILLING', 'TEAM', 'SECURITY', 'MARKETPLACE', 'WALLET');

-- CreateEnum
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "TeamRoleType" AS ENUM ('ADMIN', 'MANAGER', 'MEMBER', 'VIEWER');

-- CreateEnum
CREATE TYPE "AccessLevel" AS ENUM ('READ', 'WRITE', 'ADMIN', 'NONE');

-- CreateEnum
CREATE TYPE "InvitationStatus" AS ENUM ('PENDING', 'ACCEPTED', 'DECLINED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "DocumentType" AS ENUM ('BUSINESS_REGISTRATION', 'TAX_CERTIFICATE', 'IDENTITY_PROOF', 'ADDRESS_PROOF', 'BANK_STATEMENT', 'OTHER');

-- CreateEnum
CREATE TYPE "DocumentStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "PaymentMethodType" AS ENUM ('CREDIT_CARD', 'BANK_TRANSFER', 'CRYPTO_WALLET', 'INVOICE');

-- CreateEnum
CREATE TYPE "PaymentMethodStatus" AS ENUM ('ACTIVE', 'EXPIRED', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "BillingType" AS ENUM ('SUBSCRIPTION', 'LISTING_FEE', 'TRANSACTION_FEE', 'PLATFORM_FEE', 'REFUND');

-- CreateEnum
CREATE TYPE "BillingStatus" AS ENUM ('PENDING', 'PAID', 'FAILED', 'REFUNDED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "CarbonCreditDocumentType" AS ENUM ('CERTIFICATE', 'VERIFICATION_REPORT', 'PROJECT_DESCRIPTION', 'METHODOLOGY', 'MONITORING_REPORT', 'VALIDATION_REPORT', 'OTHER');

-- CreateEnum
CREATE TYPE "ComplianceStatus" AS ENUM ('PENDING', 'IN_REVIEW', 'APPROVED', 'REJECTED', 'EXPIRED', 'REQUIRES_UPDATE');

-- CreateEnum
CREATE TYPE "KycLevel" AS ENUM ('NONE', 'BASIC', 'INTERMEDIATE', 'ADVANCED', 'ENTERPRISE');

-- CreateEnum
CREATE TYPE "ComplianceRiskLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "AlertStatus" AS ENUM ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'FALSE_POSITIVE');

-- CreateEnum
CREATE TYPE "ComplianceDocumentType" AS ENUM ('IDENTITY', 'ADDRESS_PROOF', 'BUSINESS_REGISTRATION', 'TAX_CERTIFICATE', 'BANK_STATEMENT', 'PROJECT_DESCRIPTION', 'METHODOLOGY', 'VERIFICATION_REPORT', 'VALIDATION_REPORT', 'REGISTRY_CERTIFICATE', 'MONITORING_REPORT', 'OTHER');

-- CreateEnum
CREATE TYPE "ComplianceCheckType" AS ENUM ('KYC', 'AML', 'TRANSACTION_SCREENING', 'WALLET_SCREENING', 'CARBON_CREDIT_VERIFICATION', 'REGULATORY_CHECK', 'SANCTIONS_CHECK', 'PEP_CHECK', 'CUSTOM');

-- CreateEnum
CREATE TYPE "ComplianceCheckResult" AS ENUM ('PASS', 'FAIL', 'WARNING', 'MANUAL_REVIEW', 'INCONCLUSIVE');

-- CreateEnum
CREATE TYPE "LoggingFrequency" AS ENUM ('REAL_TIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY');

-- CreateEnum
CREATE TYPE "DataSource" AS ENUM ('MANUAL', 'CSV_UPLOAD', 'API_INTEGRATION', 'IOT_DEVICE');

-- CreateEnum
CREATE TYPE "CorrectionStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "BaselineType" AS ENUM ('GRID_EMISSION_FACTOR', 'HISTORICAL_DATA', 'BENCHMARK', 'COMBINED_MARGIN');

-- CreateEnum
CREATE TYPE "ApiIntegrationType" AS ENUM ('SMART_METER', 'IOT_SENSOR', 'WEATHER_API', 'GRID_DATA', 'EQUIPMENT_MONITORING', 'CUSTOM');

-- CreateEnum
CREATE TYPE "IntegrationStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'ERROR', 'MAINTENANCE');

-- CreateEnum
CREATE TYPE "FinancialMetricType" AS ENUM ('TRANSACTION_VOLUME', 'REVENUE', 'EXPENSE', 'WALLET_BALANCE', 'CARBON_ASSET_VALUE', 'PROFIT_LOSS', 'FEE_REVENUE', 'TRADING_VOLUME', 'AVERAGE_TRANSACTION_SIZE', 'RETIREMENT_VOLUME', 'TOKENIZATION_VOLUME');

-- CreateEnum
CREATE TYPE "MetricStatus" AS ENUM ('ABOVE_TARGET', 'ON_TARGET', 'BELOW_TARGET', 'CRITICAL');

-- CreateEnum
CREATE TYPE "FinancialReportType" AS ENUM ('TRANSACTION_SUMMARY', 'REVENUE_REPORT', 'EXPENSE_REPORT', 'ASSET_VALUATION', 'PROFIT_LOSS', 'BALANCE_SHEET', 'CASH_FLOW', 'TAX_SUMMARY', 'TRADING_ACTIVITY', 'CUSTOM');

-- CreateEnum
CREATE TYPE "ReportStatus" AS ENUM ('DRAFT', 'GENERATED', 'APPROVED', 'PUBLISHED', 'ARCHIVED');

-- CreateEnum
CREATE TYPE "ComparisonType" AS ENUM ('MONTH_OVER_MONTH', 'QUARTER_OVER_QUARTER', 'YEAR_OVER_YEAR', 'CUSTOM');

-- CreateEnum
CREATE TYPE "AuditLogType" AS ENUM ('USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'ORGANIZATION_CREATED', 'ORGANIZATION_UPDATED', 'ORGANIZATION_DELETED', 'CARBON_CREDIT_CREATED', 'CARBON_CREDIT_UPDATED', 'CARBON_CREDIT_DELETED', 'CARBON_CREDIT_LISTED', 'CARBON_CREDIT_UNLISTED', 'CARBON_CREDIT_TOKENIZED', 'CARBON_CREDIT_RETIRED', 'BUY_ORDER_CREATED', 'SELL_ORDER_CREATED', 'ORDER_MATCHED', 'ORDER_CANCELLED', 'ORDER_UPDATED', 'ORDER_DELETED', 'TRANSACTION_CREATED', 'TRANSACTION_UPDATED', 'TRADE_EXECUTED', 'WALLET_CREATED', 'WALLET_UPDATED', 'WALLET_CONNECTED', 'WALLET_DISCONNECTED', 'WALLET_SECURITY_UPDATED', 'WALLET_ACCESS_GRANTED', 'WALLET_ACCESS_REVOKED', 'WALLET_RECOVERY_ENABLED', 'WALLET_RECOVERY_DISABLED', 'WALLET_LIMITS_UPDATED', 'WALLET_APPROVAL_REQUIRED', 'WALLET_APPROVAL_GRANTED', 'WALLET_APPROVAL_REJECTED', 'BRIDGE_TRANSACTION_INITIATED', 'BRIDGE_TRANSACTION_COMPLETED', 'BRIDGE_TRANSACTION_FAILED', 'GAS_SETTINGS_UPDATED', 'SUBSCRIPTION_CREATED', 'SUBSCRIPTION_UPDATED', 'LOGIN_SUCCESS', 'LOGIN_FAILED', 'PASSWORD_RESET', 'EMAIL_VERIFIED', 'TEAM_CREATED', 'TEAM_UPDATED', 'TEAM_DELETED', 'INVITATION_SENT', 'INVITATION_ACCEPTED', 'DOCUMENT_UPLOADED', 'DOCUMENT_VERIFIED', 'PAYMENT_PROCESSED', 'BILLING_CREATED', 'VERIFICATION_REQUESTED', 'VERIFICATION_APPROVED', 'VERIFICATION_REJECTED', 'TOKENIZATION_REQUESTED', 'BATCH_VERIFICATION_REQUESTED', 'KYC_VERIFICATION_REQUESTED', 'KYC_VERIFICATION_APPROVED', 'KYC_VERIFICATION_REJECTED', 'AML_CHECK_PERFORMED', 'TAX_REPORT_GENERATED', 'TRANSACTION_AUDITED', 'TRANSACTION_FLAGGED', 'TRANSACTION_RECONCILED', 'ASSET_VALUATION_CREATED', 'ASSET_VALUATION_UPDATED', 'ASSET_VALUATION_APPROVED', 'FINANCIAL_METRIC_CREATED', 'FINANCIAL_METRIC_UPDATED', 'FINANCIAL_REPORT_GENERATED', 'FINANCIAL_REPORT_APPROVED', 'FINANCIAL_REPORT_PUBLISHED', 'PERIOD_COMPARISON_CREATED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'ROLE_CREATED', 'ROLE_UPDATED', 'ROLE_DELETED', 'PERMISSION_REQUESTED', 'PERMISSION_REQUEST_APPROVED', 'PERMISSION_REQUEST_REJECTED', 'TEMPORARY_PERMISSION_GRANTED', 'TEMPORARY_PERMISSION_EXPIRED', 'ORGANIZATION_FEES_UPDATED', 'SETTINGS_UPDATED', 'PROJECT_CREATED', 'PROJECT_UPDATED', 'PROJECT_DELETED', 'PROJECT_STATUS_CHANGED', 'PROJECT_APPROVED', 'PROJECT_REJECTED', 'PROJECT_SUSPENDED', 'PROJECT_REACTIVATED', 'DATA_ENTRY_MANUAL', 'DATA_ENTRY_CSV', 'DATA_ENTRY_API', 'DATA_ENTRY_IOT', 'DATA_EDITED', 'DATA_DELETED', 'DATA_APPROVED', 'DATA_REJECTED', 'DATA_VERIFIED', 'DATA_CORRECTION_REQUESTED', 'DATA_CORRECTION_APPROVED', 'DATA_CORRECTION_REJECTED', 'MONITORING_DATA_CREATED', 'MONITORING_DATA_UPDATED', 'MONITORING_DATA_DELETED', 'MONITORING_DATA_VERIFIED', 'MONITORING_DATA_REJECTED', 'BASELINE_CONFIG_CREATED', 'BASELINE_CONFIG_UPDATED', 'EMISSION_CALCULATION_CREATED', 'EMISSION_CALCULATION_UPDATED', 'API_INTEGRATION_CREATED', 'API_INTEGRATION_UPDATED', 'API_INTEGRATION_DELETED', 'API_SYNC_COMPLETED', 'API_SYNC_FAILED');

-- CreateEnum
CREATE TYPE "RequestStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED', 'EXPIRED');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "password" TEXT,
    "role" "UserRole" NOT NULL DEFAULT 'ORGANIZATION_USER',
    "emailVerified" TIMESTAMP(3),
    "jobTitle" TEXT,
    "departmentName" TEXT,
    "phoneNumber" TEXT,
    "profileImage" TEXT,
    "bio" TEXT,
    "lastLoginAt" TIMESTAMP(3),
    "twoFactorEnabled" BOOLEAN NOT NULL DEFAULT false,
    "preferences" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT,
    "departmentId" TEXT,
    "divisionId" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OnboardingState" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "currentStep" TEXT NOT NULL DEFAULT 'organization_details',
    "organizationId" TEXT,
    "skippedSteps" JSONB NOT NULL DEFAULT '[]',
    "completedSteps" JSONB NOT NULL DEFAULT '[]',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OnboardingState_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationDraft" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "website" TEXT,
    "industry" TEXT,
    "size" "OrganizationSize",
    "legalName" TEXT,
    "registrationNumber" TEXT,
    "taxId" TEXT,
    "country" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "phoneNumber" TEXT,
    "email" TEXT,
    "foundedYear" INTEGER,
    "currentStep" INTEGER DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrganizationDraft_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Organization" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "website" TEXT,
    "logo" TEXT,
    "status" "OrganizationStatus" NOT NULL DEFAULT 'PENDING',
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "legalName" TEXT,
    "registrationNumber" TEXT,
    "taxId" TEXT,
    "country" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "phoneNumber" TEXT,
    "industry" TEXT,
    "size" "OrganizationSize",
    "foundedYear" INTEGER,
    "primaryContact" TEXT,
    "primaryContactEmail" TEXT,
    "primaryContactPhone" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "transactionFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 0.01,
    "listingFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 0.005,
    "subscriptionFee" DOUBLE PRECISION NOT NULL DEFAULT 0.0,

    CONSTRAINT "Organization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "spvs" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "purpose" TEXT,
    "jurisdiction" TEXT,
    "status" "SPVStatus" NOT NULL DEFAULT 'ACTIVE',
    "establishedDate" TIMESTAMP(3),
    "legalStructure" TEXT,
    "registrationNumber" TEXT,
    "taxId" TEXT,
    "address" TEXT,
    "description" TEXT,
    "country" TEXT,
    "legalEntityId" TEXT,
    "email" TEXT,
    "contact" TEXT,
    "projectCategories" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "metadata" JSONB,
    "gstNumber" TEXT,
    "cinNumber" TEXT,
    "panNumber" TEXT,
    "incorporationDate" TIMESTAMP(3),
    "registeredAddress" TEXT,
    "contactPersonName" TEXT,
    "contactPersonEmail" TEXT,
    "contactPersonMobile" TEXT,
    "bankAccountNumber" TEXT,
    "ifscCode" TEXT,
    "verificationStatus" "SPVVerificationStatus" NOT NULL DEFAULT 'PENDING_VERIFICATION',
    "verificationNotes" TEXT,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "spvs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "spv_documents" (
    "id" TEXT NOT NULL,
    "spvId" TEXT NOT NULL,
    "documentType" "SPVDocumentType" NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "fileSize" INTEGER,
    "mimeType" TEXT,
    "uploadedBy" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "spv_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationFeeHistory" (
    "id" TEXT NOT NULL,
    "listingFeeRate" DOUBLE PRECISION NOT NULL,
    "transactionFeeRate" DOUBLE PRECISION NOT NULL,
    "subscriptionFee" DOUBLE PRECISION NOT NULL,
    "effectiveFrom" TIMESTAMP(3) NOT NULL,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "OrganizationFeeHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Project" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "ProjectType" NOT NULL,
    "status" "ProjectStatus" NOT NULL DEFAULT 'PENDING',
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "location" TEXT,
    "country" TEXT,
    "coordinates" TEXT,
    "area" DOUBLE PRECISION,
    "externalProjectId" TEXT,
    "registryId" TEXT,
    "standard" TEXT,
    "methodology" TEXT,
    "methodologyVersion" TEXT,
    "estimatedReductions" DOUBLE PRECISION,
    "actualReductions" DOUBLE PRECISION,
    "verifier" TEXT,
    "validator" TEXT,
    "images" TEXT[],
    "budget" DOUBLE PRECISION,
    "roi" DOUBLE PRECISION,
    "sdgs" TEXT[],
    "tags" TEXT[],
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,
    "spvId" TEXT,

    CONSTRAINT "Project_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectDocument" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "ProjectDocumentType" NOT NULL,
    "url" TEXT NOT NULL,
    "status" "DocumentStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "projectId" TEXT NOT NULL,

    CONSTRAINT "ProjectDocument_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectVerification" (
    "id" TEXT NOT NULL,
    "status" "VerificationStatus" NOT NULL,
    "verifier" TEXT,
    "verifierEmail" TEXT,
    "notes" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,
    "projectId" TEXT NOT NULL,

    CONSTRAINT "ProjectVerification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectFinancialMetric" (
    "id" TEXT NOT NULL,
    "metricType" "FinancialMetricType" NOT NULL,
    "name" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "previousValue" DOUBLE PRECISION,
    "changePercent" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "period" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "target" DOUBLE PRECISION,
    "status" "MetricStatus",
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "projectId" TEXT NOT NULL,

    CONSTRAINT "ProjectFinancialMetric_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Wallet" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "address" TEXT NOT NULL,
    "network" TEXT NOT NULL,
    "chainId" INTEGER NOT NULL,
    "isTestnet" BOOLEAN NOT NULL DEFAULT true,
    "walletType" "WalletType" NOT NULL DEFAULT 'GENERAL',
    "purpose" TEXT,
    "encryptedKey" TEXT,
    "isSmartWallet" BOOLEAN NOT NULL DEFAULT false,
    "smartAccountAddress" TEXT,
    "ownerAddress" TEXT,
    "factoryAddress" TEXT,
    "implementationAddress" TEXT,
    "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "lastSyncedAt" TIMESTAMP(3),
    "securityScore" INTEGER,
    "recoveryEnabled" BOOLEAN NOT NULL DEFAULT false,
    "recoveryType" "WalletRecoveryType",
    "recoveryData" JSONB,
    "transactionLimitDaily" DOUBLE PRECISION,
    "transactionLimitPerTx" DOUBLE PRECISION,
    "requireApprovals" BOOLEAN NOT NULL DEFAULT false,
    "approvalThreshold" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,
    "projectId" TEXT,

    CONSTRAINT "Wallet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletSecuritySetting" (
    "id" TEXT NOT NULL,
    "twoFactorEnabled" BOOLEAN NOT NULL DEFAULT false,
    "twoFactorType" TEXT,
    "whitelistedAddresses" JSONB,
    "blacklistedAddresses" JSONB,
    "delayedWithdrawals" BOOLEAN NOT NULL DEFAULT false,
    "withdrawalDelayHours" INTEGER,
    "notificationsEnabled" BOOLEAN NOT NULL DEFAULT true,
    "autoLockEnabled" BOOLEAN NOT NULL DEFAULT false,
    "autoLockTimeoutMinutes" INTEGER,
    "spendingNotifications" BOOLEAN NOT NULL DEFAULT true,
    "unusualActivityDetection" BOOLEAN NOT NULL DEFAULT true,
    "lastSecurityReview" TIMESTAMP(3),
    "securityReviewFrequency" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "walletId" TEXT NOT NULL,

    CONSTRAINT "WalletSecuritySetting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletAccessControl" (
    "id" TEXT NOT NULL,
    "walletId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessLevel" "WalletAccessLevel" NOT NULL,
    "canApprove" BOOLEAN NOT NULL DEFAULT false,
    "canInitiate" BOOLEAN NOT NULL DEFAULT false,
    "canView" BOOLEAN NOT NULL DEFAULT true,
    "customLimits" JSONB,
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WalletAccessControl_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletAuditLog" (
    "id" TEXT NOT NULL,
    "walletId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "userId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "details" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WalletAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BridgeTransaction" (
    "id" TEXT NOT NULL,
    "sourceWalletId" TEXT NOT NULL,
    "sourceNetwork" TEXT NOT NULL,
    "sourceChainId" INTEGER NOT NULL,
    "destinationAddress" TEXT NOT NULL,
    "destinationNetwork" TEXT NOT NULL,
    "destinationChainId" INTEGER NOT NULL,
    "tokenAddress" TEXT,
    "tokenSymbol" TEXT,
    "amount" TEXT NOT NULL,
    "fee" TEXT,
    "status" "BridgeStatus" NOT NULL DEFAULT 'PENDING',
    "sourceTxHash" TEXT,
    "destinationTxHash" TEXT,
    "errorMessage" TEXT,
    "bridgeProvider" TEXT NOT NULL,
    "estimatedTimeMinutes" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BridgeTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GasSetting" (
    "id" TEXT NOT NULL,
    "walletId" TEXT NOT NULL,
    "defaultGasPrice" DOUBLE PRECISION,
    "maxGasPrice" DOUBLE PRECISION,
    "defaultMaxPriorityFee" DOUBLE PRECISION,
    "defaultMaxFeePerGas" DOUBLE PRECISION,
    "gasLimitMultiplier" DOUBLE PRECISION NOT NULL DEFAULT 1.1,
    "optimizationEnabled" BOOLEAN NOT NULL DEFAULT true,
    "alertThreshold" DOUBLE PRECISION,
    "alertEnabled" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "GasSetting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CarbonCredit" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "quantity" DOUBLE PRECISION NOT NULL,
    "availableQuantity" DOUBLE PRECISION NOT NULL,
    "retiredQuantity" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "price" DOUBLE PRECISION NOT NULL,
    "minPurchaseQuantity" DOUBLE PRECISION,
    "vintage" INTEGER NOT NULL,
    "standard" TEXT NOT NULL,
    "methodology" TEXT NOT NULL,
    "location" TEXT,
    "country" TEXT,
    "externalProjectId" TEXT,
    "serialNumber" TEXT,
    "certificationDate" TIMESTAMP(3),
    "expirationDate" TIMESTAMP(3),
    "verificationBody" TEXT,
    "status" "CarbonCreditStatus" NOT NULL DEFAULT 'PENDING',
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "listingDate" TIMESTAMP(3),
    "retirementDate" TIMESTAMP(3),
    "retirementReason" TEXT,
    "retirementBeneficiary" TEXT,
    "images" TEXT[],
    "tokenId" TEXT,
    "contractAddress" TEXT,
    "chainId" INTEGER,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,

    CONSTRAINT "CarbonCredit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Tokenization" (
    "id" TEXT NOT NULL,
    "tokenId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "network" TEXT NOT NULL,
    "chainId" INTEGER,
    "contractAddress" TEXT,
    "transactionHash" TEXT,
    "status" "TokenizationStatus" NOT NULL DEFAULT 'PENDING',
    "tokenStandard" "TokenStandard" NOT NULL DEFAULT 'ERC20',
    "tokenName" TEXT,
    "tokenSymbol" TEXT,
    "tokenDecimals" INTEGER NOT NULL DEFAULT 18,
    "tokenizationMethod" "TokenizationMethod" NOT NULL DEFAULT 'STANDARD',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "carbonCreditId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "walletId" TEXT,

    CONSTRAINT "Tokenization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TokenizationStep" (
    "id" TEXT NOT NULL,
    "step" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "details" JSONB,
    "errorMessage" TEXT,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "tokenizationId" TEXT NOT NULL,

    CONSTRAINT "TokenizationStep_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketplaceListing" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" "MarketplaceStatus" NOT NULL DEFAULT 'PENDING',
    "quantity" DOUBLE PRECISION NOT NULL,
    "availableQuantity" DOUBLE PRECISION NOT NULL,
    "minPurchaseQuantity" DOUBLE PRECISION,
    "price" DOUBLE PRECISION,
    "pricingStrategy" "PricingStrategy" NOT NULL DEFAULT 'FIXED',
    "auctionEndTime" TIMESTAMP(3),
    "auctionReservePrice" DOUBLE PRECISION,
    "auctionMinIncrement" DOUBLE PRECISION,
    "dynamicPricingRules" JSONB,
    "tieredPricingRules" JSONB,
    "visibility" "ListingVisibility" NOT NULL DEFAULT 'PUBLIC',
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "tags" TEXT[],
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "carbonCreditId" TEXT NOT NULL,

    CONSTRAINT "MarketplaceListing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketplaceWatchlist" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "organizationId" TEXT,

    CONSTRAINT "MarketplaceWatchlist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WatchlistItem" (
    "id" TEXT NOT NULL,
    "priceAlertEnabled" BOOLEAN NOT NULL DEFAULT false,
    "priceAlertThreshold" DOUBLE PRECISION,
    "priceAlertDirection" "PriceAlertDirection",
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "watchlistId" TEXT NOT NULL,
    "listingId" TEXT NOT NULL,

    CONSTRAINT "WatchlistItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Retirement" (
    "id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "reason" TEXT,
    "beneficiary" TEXT,
    "tokenId" TEXT,
    "network" TEXT,
    "chainId" INTEGER,
    "contractAddress" TEXT,
    "transactionHash" TEXT,
    "status" TEXT NOT NULL DEFAULT 'COMPLETED',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "carbonCreditId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "Retirement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "type" "OrderType" NOT NULL,
    "quantity" DOUBLE PRECISION NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "status" "OrderStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "buyerId" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL,
    "carbonCreditId" TEXT NOT NULL,
    "marketplaceListingId" TEXT,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Transaction" (
    "id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "fee" DOUBLE PRECISION NOT NULL,
    "gasPrice" DOUBLE PRECISION,
    "gasLimit" INTEGER,
    "gasUsed" INTEGER,
    "maxFeePerGas" DOUBLE PRECISION,
    "maxPriorityFeePerGas" DOUBLE PRECISION,
    "type" "TransactionType" NOT NULL,
    "status" "TransactionStatus" NOT NULL DEFAULT 'PENDING',
    "transactionHash" TEXT,
    "blockNumber" INTEGER,
    "network" TEXT,
    "chainId" INTEGER,
    "tokenAddress" TEXT,
    "tokenSymbol" TEXT,
    "tokenDecimals" INTEGER,
    "counterpartyAddress" TEXT,
    "counterpartyName" TEXT,
    "purpose" TEXT,
    "category" "TransactionCategory",
    "notes" TEXT,
    "tags" TEXT[],
    "isRecurring" BOOLEAN NOT NULL DEFAULT false,
    "recurringId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "walletId" TEXT NOT NULL,
    "orderId" TEXT,
    "transactionAuditId" TEXT,
    "assetValuationId" TEXT,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionAudit" (
    "id" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "status" "AuditStatus" NOT NULL DEFAULT 'PENDING',
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "notes" TEXT,
    "flagged" BOOLEAN NOT NULL DEFAULT false,
    "flagReason" TEXT,
    "flaggedBy" TEXT,
    "flaggedAt" TIMESTAMP(3),
    "reconciled" BOOLEAN NOT NULL DEFAULT false,
    "reconciledBy" TEXT,
    "reconciledAt" TIMESTAMP(3),
    "documentUrls" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TransactionAudit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AssetValuation" (
    "id" TEXT NOT NULL,
    "assetType" "AssetType" NOT NULL,
    "assetId" TEXT NOT NULL,
    "valuationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "valuationMethod" "ValuationMethod" NOT NULL,
    "valueAmount" DOUBLE PRECISION NOT NULL,
    "valueCurrency" TEXT NOT NULL DEFAULT 'USD',
    "previousValue" DOUBLE PRECISION,
    "changePercentage" DOUBLE PRECISION,
    "valuationNotes" TEXT,
    "dataSource" TEXT,
    "confidence" INTEGER,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "transactionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "carbonCreditId" TEXT,

    CONSTRAINT "AssetValuation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subscription" (
    "id" TEXT NOT NULL,
    "plan" "SubscriptionPlan" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "status" "SubscriptionStatus" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "type" "NotificationType" NOT NULL,
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "actionUrl" TEXT,
    "actionLabel" TEXT,
    "icon" TEXT,
    "expiresAt" TIMESTAMP(3),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,
    "organizationId" TEXT,
    "emailSent" BOOLEAN NOT NULL DEFAULT false,
    "emailSentAt" TIMESTAMP(3),
    "pushSent" BOOLEAN NOT NULL DEFAULT false,
    "pushSentAt" TIMESTAMP(3),

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NotificationPreference" (
    "id" TEXT NOT NULL,
    "email" BOOLEAN NOT NULL DEFAULT true,
    "push" BOOLEAN NOT NULL DEFAULT true,
    "inApp" BOOLEAN NOT NULL DEFAULT true,
    "types" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "NotificationPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PasswordResetToken" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,

    CONSTRAINT "PasswordResetToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "VerificationToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Token" (
    "id" TEXT NOT NULL,
    "contractAddress" TEXT NOT NULL,
    "name" TEXT,
    "symbol" TEXT,
    "decimals" INTEGER,
    "balance" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "walletId" TEXT NOT NULL,

    CONSTRAINT "Token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NFT" (
    "id" TEXT NOT NULL,
    "contractAddress" TEXT NOT NULL,
    "tokenId" TEXT NOT NULL,
    "name" TEXT,
    "description" TEXT,
    "tokenType" TEXT,
    "metadata" JSONB,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "walletId" TEXT NOT NULL,

    CONSTRAINT "NFT_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Team" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "permissions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Team_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TeamMember" (
    "id" TEXT NOT NULL,
    "role" "TeamRoleType" NOT NULL DEFAULT 'MEMBER',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "teamId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "teamRoleId" TEXT,

    CONSTRAINT "TeamMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TeamRole" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "teamId" TEXT NOT NULL,
    "permissions" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TeamRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResourceScope" (
    "id" TEXT NOT NULL,
    "teamId" TEXT NOT NULL,
    "resourceType" TEXT NOT NULL,
    "resourceId" TEXT,
    "accessLevel" "AccessLevel" NOT NULL DEFAULT 'READ',
    "conditions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ResourceScope_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invitation" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "role" "UserRole" NOT NULL DEFAULT 'ORGANIZATION_USER',
    "teamId" TEXT,
    "expires" TIMESTAMP(3) NOT NULL,
    "status" "InvitationStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Invitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "DocumentType" NOT NULL,
    "url" TEXT NOT NULL,
    "status" "DocumentStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PaymentMethod" (
    "id" TEXT NOT NULL,
    "type" "PaymentMethodType" NOT NULL,
    "name" TEXT NOT NULL,
    "status" "PaymentMethodStatus" NOT NULL DEFAULT 'ACTIVE',
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "lastFour" TEXT,
    "expiryMonth" INTEGER,
    "expiryYear" INTEGER,
    "billingAddress" TEXT,
    "billingCity" TEXT,
    "billingState" TEXT,
    "billingZip" TEXT,
    "billingCountry" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "PaymentMethod_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BillingRecord" (
    "id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "description" TEXT NOT NULL,
    "status" "BillingStatus" NOT NULL DEFAULT 'PENDING',
    "dueDate" TIMESTAMP(3),
    "paidDate" TIMESTAMP(3),
    "invoiceNumber" TEXT,
    "invoiceUrl" TEXT,
    "receiptUrl" TEXT,
    "type" "BillingType" NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,
    "paymentMethodId" TEXT,

    CONSTRAINT "BillingRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CarbonCreditDocument" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "CarbonCreditDocumentType" NOT NULL,
    "url" TEXT NOT NULL,
    "status" "DocumentStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "carbonCreditId" TEXT NOT NULL,

    CONSTRAINT "CarbonCreditDocument_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CarbonCreditVerification" (
    "id" TEXT NOT NULL,
    "status" "VerificationStatus" NOT NULL,
    "verifier" TEXT,
    "verifierEmail" TEXT,
    "notes" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,
    "carbonCreditId" TEXT NOT NULL,

    CONSTRAINT "CarbonCreditVerification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CarbonCreditPrice" (
    "id" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reason" TEXT,
    "carbonCreditId" TEXT NOT NULL,

    CONSTRAINT "CarbonCreditPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" TEXT NOT NULL,
    "type" "AuditLogType" NOT NULL,
    "description" TEXT NOT NULL,
    "metadata" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ComplianceReport" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT NOT NULL,
    "format" TEXT NOT NULL DEFAULT 'PDF',
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "filters" JSONB,
    "data" JSONB,
    "url" TEXT,
    "generatedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "ComplianceReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KycVerification" (
    "id" TEXT NOT NULL,
    "status" "ComplianceStatus" NOT NULL DEFAULT 'PENDING',
    "level" "KycLevel" NOT NULL DEFAULT 'NONE',
    "lastChecked" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "verifier" TEXT,
    "verificationDate" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "additionalInfo" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "KycVerification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KycVerificationHistory" (
    "id" TEXT NOT NULL,
    "status" "ComplianceStatus" NOT NULL,
    "notes" TEXT,
    "verifier" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "kycVerificationId" TEXT NOT NULL,

    CONSTRAINT "KycVerificationHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AmlCheck" (
    "id" TEXT NOT NULL,
    "status" "ComplianceStatus" NOT NULL DEFAULT 'PENDING',
    "riskLevel" "ComplianceRiskLevel" NOT NULL DEFAULT 'LOW',
    "lastChecked" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "checkMethod" TEXT,
    "checkProvider" TEXT,
    "referenceId" TEXT,
    "findings" JSONB,
    "actionTaken" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "AmlCheck_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AmlAlert" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "riskLevel" "ComplianceRiskLevel" NOT NULL,
    "status" "AlertStatus" NOT NULL DEFAULT 'OPEN',
    "source" TEXT NOT NULL,
    "relatedEntity" TEXT,
    "relatedEntityId" TEXT,
    "assignedTo" TEXT,
    "resolvedBy" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "resolutionNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "amlCheckId" TEXT NOT NULL,

    CONSTRAINT "AmlAlert_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AmlRule" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "ruleType" TEXT NOT NULL,
    "conditions" JSONB NOT NULL,
    "actions" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "amlCheckId" TEXT NOT NULL,

    CONSTRAINT "AmlRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ComplianceDocument" (
    "id" TEXT NOT NULL,
    "type" "ComplianceDocumentType" NOT NULL,
    "name" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "status" "ComplianceStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "metadata" JSONB,
    "expiresAt" TIMESTAMP(3),
    "verifier" TEXT,
    "verificationDate" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,
    "kycVerificationId" TEXT,
    "carbonCreditId" TEXT,
    "projectId" TEXT,

    CONSTRAINT "ComplianceDocument_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ComplianceCheck" (
    "id" TEXT NOT NULL,
    "type" "ComplianceCheckType" NOT NULL,
    "result" "ComplianceCheckResult" NOT NULL,
    "riskLevel" "ComplianceRiskLevel" NOT NULL,
    "details" JSONB,
    "walletAddress" TEXT,
    "transactionHash" TEXT,
    "assetId" TEXT,
    "assetType" TEXT,
    "checkDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "performedBy" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "ComplianceCheck_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TaxReport" (
    "id" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "quarter" INTEGER,
    "format" TEXT NOT NULL DEFAULT 'pdf',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "url" TEXT,
    "data" JSONB,
    "taxableIncome" DOUBLE PRECISION,
    "taxableGains" DOUBLE PRECISION,
    "deductions" DOUBLE PRECISION,
    "taxOwed" DOUBLE PRECISION,
    "jurisdiction" TEXT,
    "filingDeadline" TIMESTAMP(3),
    "filedDate" TIMESTAMP(3),
    "preparedBy" TEXT,
    "reviewedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT,

    CONSTRAINT "TaxReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UnitLog" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "logDate" TIMESTAMP(3) NOT NULL,
    "frequency" "LoggingFrequency" NOT NULL DEFAULT 'MONTHLY',
    "unitType" TEXT NOT NULL,
    "quantity" DECIMAL(65,30) NOT NULL,
    "dataSource" "DataSource" NOT NULL DEFAULT 'MANUAL',
    "sourceFile" TEXT,
    "apiSource" TEXT,
    "verificationStatus" "DataVerificationStatus" NOT NULL DEFAULT 'DRAFT',
    "loggedBy" TEXT NOT NULL,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "verificationNotes" TEXT,
    "spvApprovedBy" TEXT,
    "spvApprovedAt" TIMESTAMP(3),
    "spvApprovalNotes" TEXT,
    "orgApprovedBy" TEXT,
    "orgApprovedAt" TIMESTAMP(3),
    "orgApprovalNotes" TEXT,
    "notes" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UnitLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UnitLogCorrection" (
    "id" TEXT NOT NULL,
    "unitLogId" TEXT NOT NULL,
    "originalQuantity" DECIMAL(65,30) NOT NULL,
    "correctedQuantity" DECIMAL(65,30) NOT NULL,
    "reason" TEXT NOT NULL,
    "correctedBy" TEXT NOT NULL,
    "approvedBy" TEXT,
    "status" "CorrectionStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UnitLogCorrection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BaselineConfiguration" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "baselineType" "BaselineType" NOT NULL,
    "gridEmissionFactor" DECIMAL(65,30) NOT NULL,
    "baselineYear" INTEGER NOT NULL,
    "baselineData" JSONB NOT NULL,
    "methodology" TEXT NOT NULL,
    "validFrom" TIMESTAMP(3) NOT NULL,
    "validTo" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "approvedBy" TEXT,
    "status" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BaselineConfiguration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmissionCalculation" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "calculationPeriod" TEXT NOT NULL,
    "powerGenerated" DECIMAL(65,30) NOT NULL,
    "emissionReduction" DECIMAL(65,30) NOT NULL,
    "baselineEmissions" DECIMAL(65,30) NOT NULL,
    "projectEmissions" DECIMAL(65,30) NOT NULL,
    "calculationMethod" TEXT NOT NULL,
    "calculatedBy" TEXT NOT NULL,
    "verifiedBy" TEXT,
    "status" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmissionCalculation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiIntegration" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "apiType" "ApiIntegrationType" NOT NULL,
    "endpoint" TEXT NOT NULL,
    "authMethod" TEXT NOT NULL,
    "credentials" JSONB NOT NULL,
    "dataMapping" JSONB NOT NULL,
    "frequency" "LoggingFrequency" NOT NULL,
    "lastSync" TIMESTAMP(3),
    "status" "IntegrationStatus" NOT NULL DEFAULT 'ACTIVE',
    "errorCount" INTEGER NOT NULL DEFAULT 0,
    "lastError" TEXT,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ApiIntegration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FinancialMetric" (
    "id" TEXT NOT NULL,
    "metricType" "FinancialMetricType" NOT NULL,
    "name" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "previousValue" DOUBLE PRECISION,
    "changePercent" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "period" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "target" DOUBLE PRECISION,
    "status" "MetricStatus",
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "FinancialMetric_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FinancialReport" (
    "id" TEXT NOT NULL,
    "reportType" "FinancialReportType" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "period" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" "ReportStatus" NOT NULL DEFAULT 'DRAFT',
    "url" TEXT,
    "data" JSONB,
    "generatedBy" TEXT,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "scheduledId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "FinancialReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PeriodComparison" (
    "id" TEXT NOT NULL,
    "comparisonType" "ComparisonType" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "period1Start" TIMESTAMP(3) NOT NULL,
    "period1End" TIMESTAMP(3) NOT NULL,
    "period2Start" TIMESTAMP(3) NOT NULL,
    "period2End" TIMESTAMP(3) NOT NULL,
    "metrics" JSONB NOT NULL,
    "changePercent" DOUBLE PRECISION,
    "insights" TEXT,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "PeriodComparison_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlatformSettings" (
    "id" TEXT NOT NULL,
    "defaultListingFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 2.5,
    "defaultTransactionFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "defaultSubscriptionFee" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "minListingFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 0.5,
    "maxListingFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 5.0,
    "minTransactionFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 0.1,
    "maxTransactionFeeRate" DOUBLE PRECISION NOT NULL DEFAULT 3.0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PlatformSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Permission" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomRole" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isSystemRole" BOOLEAN NOT NULL DEFAULT false,
    "parentRoleId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT,

    CONSTRAINT "CustomRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RolePermission" (
    "id" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "conditions" JSONB,

    CONSTRAINT "RolePermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserCustomRole" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "grantedBy" TEXT,
    "scope" JSONB,

    CONSTRAINT "UserCustomRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PermissionGrant" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "grantedBy" TEXT,
    "conditions" JSONB,
    "scope" JSONB,

    CONSTRAINT "PermissionGrant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResourcePermission" (
    "id" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "resourceType" TEXT NOT NULL,
    "resourceId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "conditions" JSONB,

    CONSTRAINT "ResourcePermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TemporaryPermission" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "reason" TEXT NOT NULL,
    "grantedBy" TEXT NOT NULL,
    "resourceType" TEXT,
    "resourceId" TEXT,
    "conditions" JSONB,

    CONSTRAINT "TemporaryPermission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PermissionRequest" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "status" "RequestStatus" NOT NULL DEFAULT 'PENDING',
    "reason" TEXT NOT NULL,
    "resourceType" TEXT,
    "resourceId" TEXT,
    "duration" INTEGER,
    "approverId" TEXT,
    "approvalNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "PermissionRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PermissionUsageLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,
    "resourceType" TEXT,
    "resourceId" TEXT,
    "action" TEXT NOT NULL,
    "success" BOOLEAN NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "metadata" JSONB,

    CONSTRAINT "PermissionUsageLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Department" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "code" TEXT,
    "parentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Division" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "code" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,
    "departmentId" TEXT,

    CONSTRAINT "Division_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationRbacSettings" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "enableCustomRoles" BOOLEAN NOT NULL DEFAULT false,
    "enableResourcePermissions" BOOLEAN NOT NULL DEFAULT false,
    "enableRoleHierarchy" BOOLEAN NOT NULL DEFAULT false,
    "enableTemporaryAccess" BOOLEAN NOT NULL DEFAULT false,
    "enablePermissionRequests" BOOLEAN NOT NULL DEFAULT false,
    "permissionRequestExpiry" INTEGER NOT NULL DEFAULT 72,
    "temporaryAccessMaxDuration" INTEGER NOT NULL DEFAULT 168,
    "requireApprovalForRoles" BOOLEAN NOT NULL DEFAULT true,
    "requireApprovalForPermissions" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrganizationRbacSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletGuardian" (
    "id" TEXT NOT NULL,
    "walletId" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "description" TEXT,

    CONSTRAINT "WalletGuardian_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletRecovery" (
    "id" TEXT NOT NULL,
    "walletId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "initiator" TEXT NOT NULL,
    "timelock" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "executedAt" TIMESTAMP(3),
    "newOwner" TEXT,
    "description" TEXT,

    CONSTRAINT "WalletRecovery_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalletRecoveryApproval" (
    "id" TEXT NOT NULL,
    "recoveryId" TEXT NOT NULL,
    "guardianId" TEXT NOT NULL,
    "approvedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "signature" TEXT,
    "notes" TEXT,

    CONSTRAINT "WalletRecoveryApproval_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "brokers" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "legalName" TEXT,
    "description" TEXT,
    "website" TEXT,
    "email" TEXT NOT NULL,
    "phoneNumber" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "country" TEXT,
    "licenseNumber" TEXT,
    "licenseType" TEXT,
    "licenseIssuer" TEXT,
    "licenseExpiryDate" TIMESTAMP(3),
    "operatingModel" "BrokerOperatingModel" NOT NULL DEFAULT 'FULL_SERVICE',
    "commissionRate" DOUBLE PRECISION NOT NULL DEFAULT 0.025,
    "specializations" TEXT[],
    "status" "BrokerClientStatus" NOT NULL DEFAULT 'ACTIVE',
    "verificationStatus" "BrokerVerificationStatus" NOT NULL DEFAULT 'PENDING',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "brokers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "broker_clients" (
    "id" TEXT NOT NULL,
    "brokerId" TEXT NOT NULL,
    "clientType" "BrokerClientType" NOT NULL DEFAULT 'ORGANIZATION',
    "clientName" TEXT NOT NULL,
    "clientEmail" TEXT NOT NULL,
    "clientPhone" TEXT,
    "relationshipType" TEXT,
    "commissionRate" DOUBLE PRECISION,
    "contractStartDate" TIMESTAMP(3),
    "contractEndDate" TIMESTAMP(3),
    "notes" TEXT,
    "status" "BrokerClientStatus" NOT NULL DEFAULT 'ACTIVE',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT,
    "userId" TEXT,

    CONSTRAINT "broker_clients_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "broker_transactions" (
    "id" TEXT NOT NULL,
    "brokerId" TEXT NOT NULL,
    "transactionType" "BrokerTransactionType" NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "commissionAmount" DOUBLE PRECISION NOT NULL,
    "commissionRate" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "referenceId" TEXT,
    "status" "BrokerTransactionStatus" NOT NULL DEFAULT 'PENDING',
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "clientId" TEXT,

    CONSTRAINT "broker_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "broker_commissions" (
    "id" TEXT NOT NULL,
    "brokerId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "rate" DOUBLE PRECISION NOT NULL,
    "baseAmount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" "BrokerCommissionStatus" NOT NULL DEFAULT 'PENDING',
    "paidDate" TIMESTAMP(3),
    "paymentReference" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "transactionId" TEXT,

    CONSTRAINT "broker_commissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "broker_documents" (
    "id" TEXT NOT NULL,
    "brokerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "BrokerDocumentType" NOT NULL,
    "url" TEXT NOT NULL,
    "description" TEXT,
    "status" "BrokerDocumentStatus" NOT NULL DEFAULT 'PENDING',
    "verifier" TEXT,
    "verificationDate" TIMESTAMP(3),
    "expiryDate" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "broker_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "spv_users" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "spvId" TEXT NOT NULL,
    "role" "SPVUserRole" NOT NULL,
    "permissions" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "spv_users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "project_assignments" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "spvUserId" TEXT NOT NULL,
    "assignedBy" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "permissions" JSONB,

    CONSTRAINT "project_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "data_verification_logs" (
    "id" TEXT NOT NULL,
    "unitLogId" TEXT NOT NULL,
    "fromStatus" "DataVerificationStatus" NOT NULL,
    "toStatus" "DataVerificationStatus" NOT NULL,
    "verifiedBy" TEXT NOT NULL,
    "verificationNotes" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "data_verification_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "OnboardingState_userId_key" ON "OnboardingState"("userId");

-- CreateIndex
CREATE INDEX "OnboardingState_userId_idx" ON "OnboardingState"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationDraft_userId_key" ON "OrganizationDraft"("userId");

-- CreateIndex
CREATE INDEX "OrganizationDraft_userId_idx" ON "OrganizationDraft"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Wallet_address_network_chainId_key" ON "Wallet"("address", "network", "chainId");

-- CreateIndex
CREATE UNIQUE INDEX "WalletSecuritySetting_walletId_key" ON "WalletSecuritySetting"("walletId");

-- CreateIndex
CREATE UNIQUE INDEX "WalletAccessControl_walletId_userId_key" ON "WalletAccessControl"("walletId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "GasSetting_walletId_key" ON "GasSetting"("walletId");

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_organizationId_key" ON "Subscription"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "NotificationPreference_userId_key" ON "NotificationPreference"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "PasswordResetToken_token_key" ON "PasswordResetToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "Token_contractAddress_walletId_key" ON "Token"("contractAddress", "walletId");

-- CreateIndex
CREATE UNIQUE INDEX "NFT_contractAddress_tokenId_walletId_key" ON "NFT"("contractAddress", "tokenId", "walletId");

-- CreateIndex
CREATE UNIQUE INDEX "TeamMember_teamId_userId_key" ON "TeamMember"("teamId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "ResourceScope_teamId_resourceType_resourceId_key" ON "ResourceScope"("teamId", "resourceType", "resourceId");

-- CreateIndex
CREATE UNIQUE INDEX "Invitation_token_key" ON "Invitation"("token");

-- CreateIndex
CREATE UNIQUE INDEX "KycVerification_userId_key" ON "KycVerification"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "KycVerification_organizationId_key" ON "KycVerification"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "AmlCheck_userId_key" ON "AmlCheck"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "AmlCheck_organizationId_key" ON "AmlCheck"("organizationId");

-- CreateIndex
CREATE INDEX "UnitLog_projectId_logDate_idx" ON "UnitLog"("projectId", "logDate");

-- CreateIndex
CREATE INDEX "UnitLog_projectId_frequency_idx" ON "UnitLog"("projectId", "frequency");

-- CreateIndex
CREATE UNIQUE INDEX "BaselineConfiguration_projectId_key" ON "BaselineConfiguration"("projectId");

-- CreateIndex
CREATE INDEX "EmissionCalculation_projectId_calculationPeriod_idx" ON "EmissionCalculation"("projectId", "calculationPeriod");

-- CreateIndex
CREATE INDEX "ApiIntegration_projectId_idx" ON "ApiIntegration"("projectId");

-- CreateIndex
CREATE UNIQUE INDEX "Permission_name_key" ON "Permission"("name");

-- CreateIndex
CREATE UNIQUE INDEX "RolePermission_roleId_permissionId_key" ON "RolePermission"("roleId", "permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "UserCustomRole_userId_roleId_key" ON "UserCustomRole"("userId", "roleId");

-- CreateIndex
CREATE UNIQUE INDEX "PermissionGrant_userId_permissionId_key" ON "PermissionGrant"("userId", "permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "ResourcePermission_permissionId_resourceType_resourceId_key" ON "ResourcePermission"("permissionId", "resourceType", "resourceId");

-- CreateIndex
CREATE UNIQUE INDEX "TemporaryPermission_userId_permissionId_resourceType_resour_key" ON "TemporaryPermission"("userId", "permissionId", "resourceType", "resourceId");

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationRbacSettings_organizationId_key" ON "OrganizationRbacSettings"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "WalletGuardian_walletId_address_key" ON "WalletGuardian"("walletId", "address");

-- CreateIndex
CREATE UNIQUE INDEX "WalletRecoveryApproval_recoveryId_guardianId_key" ON "WalletRecoveryApproval"("recoveryId", "guardianId");

-- CreateIndex
CREATE UNIQUE INDEX "brokers_userId_key" ON "brokers"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "broker_clients_brokerId_organizationId_key" ON "broker_clients"("brokerId", "organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "broker_clients_brokerId_userId_key" ON "broker_clients"("brokerId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "spv_users_userId_key" ON "spv_users"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "project_assignments_projectId_spvUserId_key" ON "project_assignments"("projectId", "spvUserId");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_divisionId_fkey" FOREIGN KEY ("divisionId") REFERENCES "Division"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OnboardingState" ADD CONSTRAINT "OnboardingState_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationDraft" ADD CONSTRAINT "OrganizationDraft_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spvs" ADD CONSTRAINT "spvs_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spv_documents" ADD CONSTRAINT "spv_documents_spvId_fkey" FOREIGN KEY ("spvId") REFERENCES "spvs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spv_documents" ADD CONSTRAINT "spv_documents_uploadedBy_fkey" FOREIGN KEY ("uploadedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spv_documents" ADD CONSTRAINT "spv_documents_verifiedBy_fkey" FOREIGN KEY ("verifiedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationFeeHistory" ADD CONSTRAINT "OrganizationFeeHistory_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Project" ADD CONSTRAINT "Project_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Project" ADD CONSTRAINT "Project_spvId_fkey" FOREIGN KEY ("spvId") REFERENCES "spvs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectDocument" ADD CONSTRAINT "ProjectDocument_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectVerification" ADD CONSTRAINT "ProjectVerification_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectFinancialMetric" ADD CONSTRAINT "ProjectFinancialMetric_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wallet" ADD CONSTRAINT "Wallet_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wallet" ADD CONSTRAINT "Wallet_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wallet" ADD CONSTRAINT "Wallet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletSecuritySetting" ADD CONSTRAINT "WalletSecuritySetting_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletAccessControl" ADD CONSTRAINT "WalletAccessControl_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletAuditLog" ADD CONSTRAINT "WalletAuditLog_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BridgeTransaction" ADD CONSTRAINT "BridgeTransaction_sourceWalletId_fkey" FOREIGN KEY ("sourceWalletId") REFERENCES "Wallet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GasSetting" ADD CONSTRAINT "GasSetting_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCredit" ADD CONSTRAINT "CarbonCredit_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCredit" ADD CONSTRAINT "CarbonCredit_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCredit" ADD CONSTRAINT "CarbonCredit_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tokenization" ADD CONSTRAINT "Tokenization_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tokenization" ADD CONSTRAINT "Tokenization_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tokenization" ADD CONSTRAINT "Tokenization_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tokenization" ADD CONSTRAINT "Tokenization_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TokenizationStep" ADD CONSTRAINT "TokenizationStep_tokenizationId_fkey" FOREIGN KEY ("tokenizationId") REFERENCES "Tokenization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceListing" ADD CONSTRAINT "MarketplaceListing_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceListing" ADD CONSTRAINT "MarketplaceListing_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceListing" ADD CONSTRAINT "MarketplaceListing_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceWatchlist" ADD CONSTRAINT "MarketplaceWatchlist_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarketplaceWatchlist" ADD CONSTRAINT "MarketplaceWatchlist_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WatchlistItem" ADD CONSTRAINT "WatchlistItem_listingId_fkey" FOREIGN KEY ("listingId") REFERENCES "MarketplaceListing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WatchlistItem" ADD CONSTRAINT "WatchlistItem_watchlistId_fkey" FOREIGN KEY ("watchlistId") REFERENCES "MarketplaceWatchlist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Retirement" ADD CONSTRAINT "Retirement_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Retirement" ADD CONSTRAINT "Retirement_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Retirement" ADD CONSTRAINT "Retirement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_marketplaceListingId_fkey" FOREIGN KEY ("marketplaceListingId") REFERENCES "MarketplaceListing"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_parentAudit_fkey" FOREIGN KEY ("transactionAuditId") REFERENCES "TransactionAudit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_parentValuation_fkey" FOREIGN KEY ("assetValuationId") REFERENCES "AssetValuation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransactionAudit" ADD CONSTRAINT "TransactionAudit_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssetValuation" ADD CONSTRAINT "AssetValuation_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssetValuation" ADD CONSTRAINT "AssetValuation_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationPreference" ADD CONSTRAINT "NotificationPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PasswordResetToken" ADD CONSTRAINT "PasswordResetToken_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VerificationToken" ADD CONSTRAINT "VerificationToken_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Token" ADD CONSTRAINT "Token_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NFT" ADD CONSTRAINT "NFT_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Team" ADD CONSTRAINT "Team_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamMember" ADD CONSTRAINT "TeamMember_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamMember" ADD CONSTRAINT "TeamMember_teamRoleId_fkey" FOREIGN KEY ("teamRoleId") REFERENCES "TeamRole"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamMember" ADD CONSTRAINT "TeamMember_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamRole" ADD CONSTRAINT "TeamRole_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResourceScope" ADD CONSTRAINT "ResourceScope_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invitation" ADD CONSTRAINT "Invitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaymentMethod" ADD CONSTRAINT "PaymentMethod_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BillingRecord" ADD CONSTRAINT "BillingRecord_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BillingRecord" ADD CONSTRAINT "BillingRecord_paymentMethodId_fkey" FOREIGN KEY ("paymentMethodId") REFERENCES "PaymentMethod"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCreditDocument" ADD CONSTRAINT "CarbonCreditDocument_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCreditVerification" ADD CONSTRAINT "CarbonCreditVerification_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CarbonCreditPrice" ADD CONSTRAINT "CarbonCreditPrice_carbonCreditId_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceReport" ADD CONSTRAINT "ComplianceReport_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceReport" ADD CONSTRAINT "ComplianceReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KycVerification" ADD CONSTRAINT "KycVerification_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KycVerification" ADD CONSTRAINT "KycVerification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KycVerificationHistory" ADD CONSTRAINT "KycVerificationHistory_kycVerificationId_fkey" FOREIGN KEY ("kycVerificationId") REFERENCES "KycVerification"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmlCheck" ADD CONSTRAINT "AmlCheck_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmlCheck" ADD CONSTRAINT "AmlCheck_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmlAlert" ADD CONSTRAINT "AmlAlert_amlCheckId_fkey" FOREIGN KEY ("amlCheckId") REFERENCES "AmlCheck"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmlRule" ADD CONSTRAINT "AmlRule_amlCheckId_fkey" FOREIGN KEY ("amlCheckId") REFERENCES "AmlCheck"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceDocument" ADD CONSTRAINT "ComplianceDocument_carbonCreditId_compliance_fkey" FOREIGN KEY ("carbonCreditId") REFERENCES "CarbonCredit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceDocument" ADD CONSTRAINT "ComplianceDocument_kycVerificationId_fkey" FOREIGN KEY ("kycVerificationId") REFERENCES "KycVerification"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceDocument" ADD CONSTRAINT "ComplianceDocument_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceDocument" ADD CONSTRAINT "ComplianceDocument_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceDocument" ADD CONSTRAINT "ComplianceDocument_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceCheck" ADD CONSTRAINT "ComplianceCheck_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ComplianceCheck" ADD CONSTRAINT "ComplianceCheck_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TaxReport" ADD CONSTRAINT "TaxReport_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TaxReport" ADD CONSTRAINT "TaxReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLog" ADD CONSTRAINT "UnitLog_loggedBy_fkey" FOREIGN KEY ("loggedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLog" ADD CONSTRAINT "UnitLog_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLog" ADD CONSTRAINT "UnitLog_verifiedBy_fkey" FOREIGN KEY ("verifiedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLogCorrection" ADD CONSTRAINT "UnitLogCorrection_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLogCorrection" ADD CONSTRAINT "UnitLogCorrection_correctedBy_fkey" FOREIGN KEY ("correctedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UnitLogCorrection" ADD CONSTRAINT "UnitLogCorrection_unitLogId_fkey" FOREIGN KEY ("unitLogId") REFERENCES "UnitLog"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BaselineConfiguration" ADD CONSTRAINT "BaselineConfiguration_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BaselineConfiguration" ADD CONSTRAINT "BaselineConfiguration_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BaselineConfiguration" ADD CONSTRAINT "BaselineConfiguration_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmissionCalculation" ADD CONSTRAINT "EmissionCalculation_calculatedBy_fkey" FOREIGN KEY ("calculatedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmissionCalculation" ADD CONSTRAINT "EmissionCalculation_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmissionCalculation" ADD CONSTRAINT "EmissionCalculation_verifiedBy_fkey" FOREIGN KEY ("verifiedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiIntegration" ADD CONSTRAINT "ApiIntegration_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiIntegration" ADD CONSTRAINT "ApiIntegration_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FinancialMetric" ADD CONSTRAINT "FinancialMetric_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FinancialReport" ADD CONSTRAINT "FinancialReport_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PeriodComparison" ADD CONSTRAINT "PeriodComparison_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomRole" ADD CONSTRAINT "CustomRole_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomRole" ADD CONSTRAINT "CustomRole_parentRoleId_fkey" FOREIGN KEY ("parentRoleId") REFERENCES "CustomRole"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RolePermission" ADD CONSTRAINT "RolePermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "CustomRole"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserCustomRole" ADD CONSTRAINT "UserCustomRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "CustomRole"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserCustomRole" ADD CONSTRAINT "UserCustomRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PermissionGrant" ADD CONSTRAINT "PermissionGrant_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PermissionGrant" ADD CONSTRAINT "PermissionGrant_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResourcePermission" ADD CONSTRAINT "ResourcePermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemporaryPermission" ADD CONSTRAINT "TemporaryPermission_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemporaryPermission" ADD CONSTRAINT "TemporaryPermission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PermissionRequest" ADD CONSTRAINT "PermissionRequest_approverId_fkey" FOREIGN KEY ("approverId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PermissionRequest" ADD CONSTRAINT "PermissionRequest_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PermissionRequest" ADD CONSTRAINT "PermissionRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Department" ADD CONSTRAINT "Department_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Department" ADD CONSTRAINT "Department_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Division" ADD CONSTRAINT "Division_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Division" ADD CONSTRAINT "Division_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationRbacSettings" ADD CONSTRAINT "OrganizationRbacSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletGuardian" ADD CONSTRAINT "WalletGuardian_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletRecovery" ADD CONSTRAINT "WalletRecovery_walletId_fkey" FOREIGN KEY ("walletId") REFERENCES "Wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletRecoveryApproval" ADD CONSTRAINT "WalletRecoveryApproval_guardianId_fkey" FOREIGN KEY ("guardianId") REFERENCES "WalletGuardian"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalletRecoveryApproval" ADD CONSTRAINT "WalletRecoveryApproval_recoveryId_fkey" FOREIGN KEY ("recoveryId") REFERENCES "WalletRecovery"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "brokers" ADD CONSTRAINT "brokers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_clients" ADD CONSTRAINT "broker_clients_brokerId_fkey" FOREIGN KEY ("brokerId") REFERENCES "brokers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_clients" ADD CONSTRAINT "broker_clients_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_clients" ADD CONSTRAINT "broker_clients_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_transactions" ADD CONSTRAINT "broker_transactions_brokerId_fkey" FOREIGN KEY ("brokerId") REFERENCES "brokers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_transactions" ADD CONSTRAINT "broker_transactions_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "broker_clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_commissions" ADD CONSTRAINT "broker_commissions_brokerId_fkey" FOREIGN KEY ("brokerId") REFERENCES "brokers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_commissions" ADD CONSTRAINT "broker_commissions_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "broker_transactions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "broker_documents" ADD CONSTRAINT "broker_documents_brokerId_fkey" FOREIGN KEY ("brokerId") REFERENCES "brokers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spv_users" ADD CONSTRAINT "spv_users_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "spv_users" ADD CONSTRAINT "spv_users_spvId_fkey" FOREIGN KEY ("spvId") REFERENCES "spvs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_spvUserId_fkey" FOREIGN KEY ("spvUserId") REFERENCES "spv_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_assignedBy_fkey" FOREIGN KEY ("assignedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "data_verification_logs" ADD CONSTRAINT "data_verification_logs_unitLogId_fkey" FOREIGN KEY ("unitLogId") REFERENCES "UnitLog"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "data_verification_logs" ADD CONSTRAINT "data_verification_logs_verifiedBy_fkey" FOREIGN KEY ("verifiedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
