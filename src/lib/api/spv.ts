/**
 * SPV API client functions
 */

import { API_ENDPOINTS } from "@/lib/api-endpoints";
import {
  SPV,
  SPVWithOrganization,
  SPVWithDetails,
  SPVCreateData,
  AdminSPVCreateData,
  SPVUpdateData,
  SPVListResponse,
  SPVDetailsResponse,
  SPVCreateResponse,
  SPVUpdateResponse,
  SPVDeleteResponse,
  OrganizationSPVResponse,
  SPVQueryParams,
  SPVBulkOperationRequest,
  SPVBulkOperationResponse,
} from "@/types/spv";

// Base API error class
export class SPVApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = "SPVApiError";
  }
}

// Helper function to handle API responses
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorData: any = {};
    try {
      errorData = await response.json();
    } catch (e) {
      // If response is not JSON, create a generic error
      errorData = {
        message: `HTTP ${response.status}: ${response.statusText}`,
        error: response.statusText,
        code: response.status
      };
    }

    // Log detailed error information for debugging
    console.error("API Error Details:", {
      url: response.url,
      status: response.status,
      statusText: response.statusText,
      errorData
    });

    throw new SPVApiError(
      errorData.message || errorData.error || `HTTP ${response.status}: ${response.statusText}`,
      response.status,
      errorData.code
    );
  }
  return response.json();
}

// Helper function to build query string
function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

/**
 * Organization-scoped SPV API functions (for dashboard)
 */
export const organizationSPVApi = {
  /**
   * Get all SPVs for the current user's organization
   */
  async getOrganizationSPVs(): Promise<OrganizationSPVResponse> {
    const response = await fetch(API_ENDPOINTS.ORGANIZATION.ORGANIZATION_SPVS, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Include cookies for authentication
    });

    return handleApiResponse<OrganizationSPVResponse>(response);
  },

  /**
   * Create a new SPV for the current user's organization
   */
  async createSPV(data: SPVCreateData): Promise<SPVCreateResponse> {
    const response = await fetch(API_ENDPOINTS.ORGANIZATION.ORGANIZATION_SPVS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Include cookies for authentication
      body: JSON.stringify(data),
    });

    return handleApiResponse<SPVCreateResponse>(response);
  },

  /**
   * Get a specific SPV by ID for the current user's organization
   */
  async getSPVById(id: string): Promise<SPVDetailsResponse> {
    const url = `${API_ENDPOINTS.ORGANIZATION.ORGANIZATION_SPVS}/${id}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<SPVDetailsResponse>(response);
  },
};

/**
 * Admin SPV API functions (for admin panel)
 */
export const adminSPVApi = {
  /**
   * Get all SPVs across organizations with filtering and pagination
   */
  async getAllSPVs(params: SPVQueryParams = {}): Promise<SPVListResponse> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_SPVS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<SPVListResponse>(response);
  },

  /**
   * Get a specific SPV by ID
   */
  async getSPVById(id: string): Promise<SPVDetailsResponse> {
    const url = API_ENDPOINTS.ADMIN.ADMIN_SPV_BY_ID.replace("{id}", id);
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<SPVDetailsResponse>(response);
  },

  /**
   * Create a new SPV for any organization
   */
  async createSPV(data: AdminSPVCreateData): Promise<SPVCreateResponse> {
    const response = await fetch(API_ENDPOINTS.ADMIN.ADMIN_SPVS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<SPVCreateResponse>(response);
  },

  /**
   * Update a specific SPV
   */
  async updateSPV(id: string, data: SPVUpdateData): Promise<SPVUpdateResponse> {
    const url = API_ENDPOINTS.ADMIN.ADMIN_SPV_BY_ID.replace("{id}", id);
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<SPVUpdateResponse>(response);
  },

  /**
   * Delete a specific SPV
   */
  async deleteSPV(id: string): Promise<SPVDeleteResponse> {
    const url = API_ENDPOINTS.ADMIN.ADMIN_SPV_BY_ID.replace("{id}", id);
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<SPVDeleteResponse>(response);
  },

  /**
   * Perform bulk operations on SPVs
   */
  async bulkOperation(request: SPVBulkOperationRequest): Promise<SPVBulkOperationResponse> {
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_SPVS}/bulk`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });
    
    return handleApiResponse<SPVBulkOperationResponse>(response);
  },

  /**
   * Export SPVs to CSV
   */
  async exportSPVs(params: SPVQueryParams = {}): Promise<Blob> {
    const queryString = buildQueryString({ ...params, export: "csv" });
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_SPVS}/export${queryString}`, {
      method: "GET",
      headers: {
        "Accept": "text/csv",
      },
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new SPVApiError(
        errorData.message || "Failed to export SPVs",
        response.status
      );
    }
    
    return response.blob();
  },
};

/**
 * Utility functions for SPV data processing
 */
export const spvUtils = {
  /**
   * Format SPV status for display
   */
  formatStatus(status: string): string {
    const statusMap: Record<string, string> = {
      ACTIVE: "Active",
      INACTIVE: "Inactive",
      PENDING: "Pending",
      DISSOLVED: "Dissolved",
    };
    return statusMap[status] || status;
  },

  /**
   * Format SPV verification status for display
   */
  formatVerificationStatus(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING_VERIFICATION: "Pending",
      IN_REVIEW: "In Review",
      VERIFIED: "Verified",
      REJECTED: "Rejected",
      NEEDS_MORE_INFO: "Needs Info",
      SUSPENDED: "Suspended",
    };
    return statusMap[status] || status;
  },

  /**
   * Get status color for UI display
   */
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      ACTIVE: "green",
      INACTIVE: "gray",
      PENDING: "yellow",
      DISSOLVED: "red",
    };
    return colorMap[status] || "gray";
  },

  /**
   * Get verification status color for UI display
   */
  getVerificationStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      VERIFIED: "green",
      IN_REVIEW: "blue",
      PENDING_VERIFICATION: "yellow",
      REJECTED: "red",
      NEEDS_MORE_INFO: "orange",
      SUSPENDED: "gray",
    };
    return colorMap[status] || "gray";
  },

  /**
   * Format date for display
   */
  formatDate(dateString: string | null): string {
    if (!dateString) return "N/A";
    
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return "Invalid Date";
    }
  },

  /**
   * Format date and time for display
   */
  formatDateTime(dateString: string | null): string {
    if (!dateString) return "N/A";
    
    try {
      return new Date(dateString).toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Invalid Date";
    }
  },

  /**
   * Validate SPV form data
   */
  validateSPVData(data: Partial<SPVCreateData>): Record<string, string> {
    const errors: Record<string, string> = {};

    if (!data.name || data.name.trim().length < 3) {
      errors.name = "SPV name must be at least 3 characters";
    }

    // Purpose is now optional, but if provided, validate length
    if (data.purpose && data.purpose.trim().length > 500) {
      errors.purpose = "Purpose must be less than 500 characters";
    }

    if (!data.jurisdiction || data.jurisdiction.trim().length < 2) {
      errors.jurisdiction = "Jurisdiction is required";
    }

    if (!data.legalStructure || data.legalStructure.trim().length < 2) {
      errors.legalStructure = "Legal structure is required";
    }

    if (data.establishedDate) {
      const date = new Date(data.establishedDate);
      if (isNaN(date.getTime())) {
        errors.establishedDate = "Invalid date format";
      } else if (date > new Date()) {
        errors.establishedDate = "Established date cannot be in the future";
      }
    }

    return errors;
  },

  /**
   * Generate SPV summary text
   */
  generateSummary(spv: SPVWithDetails): string {
    const parts = [];
    
    if (spv.legalStructure) {
      parts.push(spv.legalStructure);
    }
    
    if (spv.jurisdiction) {
      parts.push(`in ${spv.jurisdiction}`);
    }
    
    if (spv._count.projects > 0) {
      parts.push(`managing ${spv._count.projects} project${spv._count.projects === 1 ? "" : "s"}`);
    }
    
    return parts.join(" ");
  },
};

// Export all API functions
export const spvApi = {
  organization: organizationSPVApi,
  admin: adminSPVApi,
  utils: spvUtils,
};
