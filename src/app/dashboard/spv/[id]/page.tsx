"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Building2,
  Users,
  Folder,
  ArrowLeft,
  Key,
  Shield,
  Edit,
  FileText,
  Upload,
  Check,
  Clock,
  AlertCircle,
  X,
  Eye
} from "lucide-react";
import { toast } from "sonner";
import { useSPVDetails } from "@/hooks/use-spv-details";
import { SPVUsersSection } from "@/components/spv/spv-users-section";
import { SPVProjectsSection } from "@/components/spv/spv-projects-section";
import { SPVEditForm } from "@/components/spv/spv-edit-form";
// Verification flow commented out for now
// import { SPVVerificationForm } from "@/components/spv/spv-verification-form";
import { SPVDocumentUpload } from "@/components/spv/spv-document-upload";
import { SPVDocumentViewer } from "@/components/spv/spv-document-viewer";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { SPVVerificationStatus } from "@/types/spv";

export default function SPVDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const spvId = params.id as string;
  
  const { spv, isLoading, error, fetchSPVDetails } = useSPVDetails();
  const [activeTab, setActiveTab] = useState("details");
  const [isEditMode, setIsEditMode] = useState(false);
  // Verification flow commented out for now
  // const [isVerificationFormOpen, setIsVerificationFormOpen] = useState(false);
  const [isDocumentUploadOpen, setIsDocumentUploadOpen] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | undefined>();

  // Helper function to get verification status styling
  const getVerificationStatusStyle = (status: SPVVerificationStatus) => {
    switch (status) {
      case "VERIFIED":
        return {
          className: "bg-green-100 text-green-800 border-green-200",
          icon: Check,
          label: "Verified"
        };
      case "REJECTED":
        return {
          className: "bg-red-100 text-red-800 border-red-200",
          icon: X,
          label: "Rejected"
        };
      case "IN_REVIEW":
        return {
          className: "bg-blue-100 text-blue-800 border-blue-200",
          icon: Eye,
          label: "In Review"
        };
      case "NEEDS_MORE_INFO":
        return {
          className: "bg-yellow-100 text-yellow-800 border-yellow-200",
          icon: AlertCircle,
          label: "Needs Info"
        };
      // case "PENDING_VERIFICATION":
      //   return {
      //     className: "bg-gray-100 text-gray-800 border-gray-200",
      //     icon: Clock,
      //     label: "Pending"
      //   };
      default:
        return {
          className: "bg-gray-100 text-gray-800 border-gray-200",
          icon: Shield,
          label: "Unknown"
        };
    }
  };

  useEffect(() => {
    if (spvId) {
      fetchSPVDetails(spvId);
    }
  }, [spvId, fetchSPVDetails]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !spv) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">SPV Not Found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {error || "The requested SPV could not be found."}
            </p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button> */}
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Building2 className="h-6 w-6" />
              {spv.name}
            </h1>
            <p className="text-muted-foreground">
              {spv.legalStructure}  {spv.jurisdiction}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {/* Verification Status Badge */}
          {/* {spv.verificationStatus && (() => {
            const verificationStyle = getVerificationStatusStyle(spv.verificationStatus);
            const Icon = verificationStyle.icon;
            return (
              <Badge
                variant="outline"
                className={`${verificationStyle.className}`}
              >
                <Icon className="h-3 w-3 mr-1" />
                {verificationStyle.label}
              </Badge>
            );
          })()} */}

          {/* SPV Status Badge */}
          <Badge
            variant={spv.status === "ACTIVE" ? "default" : "secondary"}
            className={
              spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
              spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
              spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
              "bg-red-100 text-red-800"
            }
          >
            {spv.status}
          </Badge>
          {/* Verification Action Buttons */}
          {/* Verification flow commented out for now */}
          {/* {spv.verificationStatus === "PENDING_VERIFICATION" && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsVerificationFormOpen(true)}
              >
                <Shield className="mr-2 h-4 w-4" />
                Complete Verification
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDocumentUploadOpen(true)}
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Documents
              </Button>
            </>
          )} */}

          {/* {spv.verificationStatus === "NEEDS_MORE_INFO" && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsVerificationFormOpen(true)}
              >
                <AlertCircle className="mr-2 h-4 w-4" />
                Update Details
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDocumentUploadOpen(true)}
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Documents
              </Button>
            </>
          )} */}

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditMode(true)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit SPV
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{spv._count.spvUsers || 0}</p>
                <p className="text-sm text-muted-foreground">Users</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Folder className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{spv._count.projects || 0}</p>
                <p className="text-sm text-muted-foreground">Projects</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm font-medium">{spv.organization.name}</p>
                <p className="text-sm text-muted-foreground">Organization</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details">SPV Details</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                SPV Information
              </CardTitle>
              <CardDescription>
                Detailed information about this Special Purpose Vehicle
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-medium mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">SPV Name</p>
                    <p className="text-sm">{spv.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge
                      variant={spv.status === "ACTIVE" ? "default" : "secondary"}
                      className={
                        spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                        spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                        spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
                        "bg-red-100 text-red-800"
                      }
                    >
                      {spv.status}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Country</p>
                    <p className="text-sm">{spv.country}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Legal Entity ID</p>
                    <p className="text-sm">{spv.legalEntityId}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Contact</p>
                    <p className="text-sm">{spv.contact}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Project Categories</p>
                    <p className="text-sm">{spv.projectCategories.join(", ")}</p>
                  </div>
                </div>
              </div>

              {/* Legal Information */}
              {(spv.legalStructure || spv.jurisdiction || spv.registrationNumber || spv.taxId) && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Legal Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {spv.legalStructure && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Legal Structure</p>
                        <p className="text-sm">{spv.legalStructure}</p>
                      </div>
                    )}
                    {spv.jurisdiction && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Jurisdiction</p>
                        <p className="text-sm">{spv.jurisdiction}</p>
                      </div>
                    )}
                    {spv.registrationNumber && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Registration Number</p>
                        <p className="text-sm">{spv.registrationNumber}</p>
                      </div>
                    )}
                    {spv.taxId && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Tax ID</p>
                        <p className="text-sm">{spv.taxId}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Purpose and Description */}
              {(spv.purpose || spv.description) && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Additional Information</h3>
                  {spv.purpose && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-muted-foreground mb-2">Purpose</p>
                      <p className="text-sm">{spv.purpose}</p>
                    </div>
                  )}
                  {spv.description && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-2">Description</p>
                      <p className="text-sm">{spv.description}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Organization Information */}
              <div>
                <h3 className="text-lg font-medium mb-4">Organization</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Organization Name</p>
                    <p className="text-sm">{spv.organization.name}</p>
                  </div>
                  {spv.organization.legalName && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Legal Name</p>
                      <p className="text-sm">{spv.organization.legalName}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Industry</p>
                    <p className="text-sm">{spv.organization.industry || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Country</p>
                    <p className="text-sm">{spv.organization.country || "N/A"}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Documents
                  </CardTitle>
                  <CardDescription>
                    Manage and view SPV documents
                  </CardDescription>
                </div>
                <Button onClick={() => setIsDocumentUploadOpen(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Documents
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <SPVDocumentViewer
                documents={spv.documents || []}
                canUpload={true}
                canVerify={false}
                spvId={spv.id}
                onUploadDocument={(documentType) => {
                  setSelectedDocumentType(documentType);
                  setIsDocumentUploadOpen(true);
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Verification tab commented out for now */}
        {/* <TabsContent value="verification" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Verification Status
              </CardTitle>
              <CardDescription>
                Current verification status and required actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {spv.verificationStatus && (() => {
                const verificationStyle = getVerificationStatusStyle(spv.verificationStatus);
                const Icon = verificationStyle.icon;
                return (
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${verificationStyle.className}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{verificationStyle.label}</h3>
                      <p className="text-sm text-muted-foreground">
                        {spv.verificationStatus === "PENDING_VERIFICATION" && "Complete verification details and upload documents to proceed"}
                        {spv.verificationStatus === "IN_REVIEW" && "Your SPV is being reviewed by our verification team"}
                        {spv.verificationStatus === "NEEDS_MORE_INFO" && "Additional information is required to complete verification"}
                        {spv.verificationStatus === "REJECTED" && "Verification was rejected. Please review feedback and resubmit"}
                        {spv.verificationStatus === "VERIFIED" && "Your SPV has been successfully verified and is ready for use"}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      {spv.verificationStatus === "PENDING_VERIFICATION" && (
                        <>
                          <Button size="sm" onClick={() => setIsVerificationFormOpen(true)}>
                            <Shield className="h-4 w-4 mr-2" />
                            Complete Details
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setIsDocumentUploadOpen(true)}>
                            <Upload className="h-4 w-4 mr-2" />
                            Upload Documents
                          </Button>
                        </>
                      )}
                      {spv.verificationStatus === "NEEDS_MORE_INFO" && (
                        <>
                          <Button size="sm" onClick={() => setIsVerificationFormOpen(true)}>
                            <AlertCircle className="h-4 w-4 mr-2" />
                            Update Details
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setIsDocumentUploadOpen(true)}>
                            <Upload className="h-4 w-4 mr-2" />
                            Upload Documents
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                );
              })()}

              {(spv.verificationNotes || spv.rejectionReason) && (
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">
                    {spv.rejectionReason ? "Rejection Reason" : "Verification Notes"}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {spv.rejectionReason || spv.verificationNotes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Verification Documents
              </CardTitle>
              <CardDescription>
                Upload and manage verification documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SPVDocumentViewer
                documents={spv.documents || []}
                canUpload={true}
                canVerify={false}
              />

              {(spv.verificationStatus === "PENDING_VERIFICATION" || spv.verificationStatus === "NEEDS_MORE_INFO") && (
                <div className="mt-4 pt-4 border-t">
                  <Button onClick={() => setIsDocumentUploadOpen(true)}>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload More Documents
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent> */}

        <TabsContent value="users">
          <SPVUsersSection
            spv={spv}
            onUserCountChange={() => fetchSPVDetails(spvId)}
          />
        </TabsContent>

        <TabsContent value="projects">
          <SPVProjectsSection spv={spv} />
        </TabsContent>

      </Tabs>

      {/* Edit SPV Dialog */}
      <Dialog open={isEditMode} onOpenChange={setIsEditMode}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit SPV Details</DialogTitle>
          </DialogHeader>
          {spv && (
            <SPVEditForm
              spv={spv}
              onSuccess={() => {
                setIsEditMode(false);
                fetchSPVDetails(spvId);
                toast.success("SPV updated successfully");
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* SPV Verification Form Dialog - Commented out for now */}
      {/* <Dialog open={isVerificationFormOpen} onOpenChange={(open) => {
        if (!open) {
          setIsVerificationFormOpen(false);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Complete SPV Verification - {spv.name}
            </DialogTitle>
            <DialogDescription>
              Complete the verification information for your SPV
            </DialogDescription>
          </DialogHeader>

          {spv && (
            <SPVVerificationForm
              isOpen={true}
              onClose={() => {}}
              onSubmit={async (data) => {
                try {
                  const response = await fetch(`/api/organizations/spvs/${spv.id}/verification`, {
                    method: 'PUT',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                  });

                  if (!response.ok) {
                    throw new Error('Failed to update verification details');
                  }

                  toast.success("Verification details updated successfully!");
                  setIsVerificationFormOpen(false);
                  fetchSPVDetails(spvId);
                } catch (error) {
                  console.error('Error updating verification details:', error);
                  toast.error("Failed to update verification details. Please try again.");
                }
              }}
              initialData={{
                name: spv.name,
                purpose: spv.purpose || "",
                legalStructure: spv.legalStructure || "",
                registrationNumber: spv.registrationNumber || "",
                jurisdiction: spv.jurisdiction || "",
                country: spv.country || "",
                gstNumber: spv.gstNumber || "",
                cinNumber: spv.cinNumber || "",
                panNumber: spv.panNumber || "",
                incorporationDate: spv.incorporationDate || "",
                establishedDate: spv.establishedDate || "",
                registeredAddress: spv.registeredAddress || "",
                address: spv.address || "",
                contactPersonName: spv.contactPersonName || "",
                contactPersonEmail: spv.contactPersonEmail || "",
                contactPersonMobile: spv.contactPersonMobile || "",
                contact: spv.contact || "",
                bankAccountNumber: spv.bankAccountNumber || "",
                ifscCode: spv.ifscCode || "",
                projectCategories: spv.projectCategories?.[0] || "",
                description: spv.description || "",
                taxId: spv.taxId || "",
                legalEntityId: spv.legalEntityId || "",
              }}
              spvId={spv.id}
            />
          )}
        </DialogContent>
      </Dialog> */}

      {/* SPV Document Upload Dialog */}
      <Dialog open={isDocumentUploadOpen} onOpenChange={(open) => {
        if (!open) {
          setIsDocumentUploadOpen(false);
          setSelectedDocumentType(undefined);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Documents - {spv.name}
            </DialogTitle>
            <DialogDescription>
              Upload the required verification documents for your SPV
            </DialogDescription>
          </DialogHeader>

          {spv && (
            <SPVDocumentUpload
              spvId={spv.id}
              preSelectedDocumentType={selectedDocumentType as any}
              existingDocuments={spv.documents}
              onUploadComplete={async (document) => {
                toast.success(`Document "${document.fileName}" uploaded successfully!`);
                // Small delay to ensure the document is saved before refreshing
                setTimeout(() => {
                  fetchSPVDetails(spvId); // Refresh to show new document
                }, 500);
                setIsDocumentUploadOpen(false);
                setSelectedDocumentType(undefined);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
