"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { useAdminSPVs, useAdminSPVDetails, useAdminSPVMutations } from "@/hooks/use-admin-spvs";
import { spvUtils } from "@/lib/api/spv";
import { SPVStatus, SPVWithOrganization, AdminSPVCreateData, SPVUpdateData } from "@/types/spv";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON>ertDescription } from "@/components/ui/alert";
import { SPVDataTable } from "@/components/spv/spv-data-table";
import { SPVFilters, SPVStats } from "@/components/spv/spv-filters";
import { EnhancedSPVCreationForm } from "@/components/spv/enhanced-spv-creation-form";
import { AdminSPVForm } from "@/components/spv/admin-spv-form";
import { SPVDetailsView } from "@/components/spv/spv-details-view";
import { toast } from "sonner";

export default function AdminSPVManagementClient() {
  const {
    spvs,
    organizations,
    pagination,
    filters,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useAdminSPVs();

  const { createSPV, updateSPV, deleteSPV, isCreating, isUpdating, isDeleting } = useAdminSPVMutations(refetch);

  const [selectedSPV, setSelectedSPV] = useState<SPVWithOrganization | null>(null);
  const [detailsSPVId, setDetailsSPVId] = useState<string | null>(null);
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);

  const { spv: detailsSPV, isLoading: isLoadingDetails } = useAdminSPVDetails(detailsSPVId || "");

  const handleCreateSPV = async (data: AdminSPVCreateData) => {
    try {
      const result = await createSPV(data);

      // Close modal immediately
      setIsCreateFormOpen(false);
    } catch (error) {
      toast.error("Failed to create SPV");
    }
  };

  const handleUpdateSPV = async (data: SPVUpdateData) => {
    if (!selectedSPV) return;

    try {
      await updateSPV(selectedSPV.id, data);
      toast.success("SPV updated successfully");
      setIsEditFormOpen(false);
      setSelectedSPV(null);
    } catch (error) {
      toast.error("Failed to update SPV");
    }
  };



  const handleDocumentVerification = async (documentId: string, verified: boolean, notes?: string) => {
    if (!detailsSPVId) return;

    try {
      const response = await fetch(`/api/organizations/spvs/${detailsSPVId}/documents/${documentId}/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          verified,
          notes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to verify document");
      }

      const result = await response.json();
      toast.success(result.message || "Document verification updated successfully");

      // Refresh SPV details to show updated verification status
      refetch();
    } catch (error) {
      console.error("Document verification error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to verify document");
    }
  };

  const handleSPVVerificationAction = async (action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO', notes?: string) => {
    if (!detailsSPVId) return;

    try {
      const response = await fetch(`/api/organizations/spvs/${detailsSPVId}/verification/actions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          notes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update SPV verification status");
      }

      const result = await response.json();
      toast.success(result.message || "SPV verification status updated successfully");

      // Refresh SPV details to show updated verification status
      refetch();
    } catch (error) {
      console.error("SPV verification action error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update SPV verification status");
    }
  };

  const handleDeleteSPV = async (spv: SPVWithOrganization) => {
    if (!confirm(`Are you sure you want to delete "${spv.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteSPV(spv.id);
      toast.success("SPV deleted successfully");
    } catch (error) {
      toast.error("Failed to delete SPV");
    }
  };

  const handleViewSPV = (spv: SPVWithOrganization) => {
    setDetailsSPVId(spv.id);
    setIsDetailsOpen(true);
  };

  const handleEditSPV = (spv: SPVWithOrganization) => {
    setSelectedSPV(spv);
    setIsEditFormOpen(true);
  };

  const handleSort = (sortBy: "name" | "createdAt" | "status" | "organization" | "verificationStatus") => {
    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === "asc" ? "desc" : "asc";
    updateFilters({ sortBy, sortOrder: newSortOrder });
  };

  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Error loading SPVs: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="admin-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">SPV Management</h1>
          <p className="text-muted-foreground">
            Manage Special Purpose Vehicles, document verification, and user access
          </p>
        </div>
        <Button onClick={() => setIsCreateFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create SPV
        </Button>
      </div>

      {/* Filters */}
      <SPVFilters
        filters={filters}
        organizations={organizations}
        onFiltersChange={updateFilters}
        showOrganizationFilter={true}
      />

      {/* Stats */}
      <SPVStats
        totalSPVs={pagination.totalCount}
        activeSPVs={spvs.filter(spv => spv.status === "ACTIVE").length}
        pendingSPVs={spvs.filter(spv => spv.status === "PENDING").length}
        inactiveSPVs={spvs.filter(spv => spv.status === "INACTIVE").length}
        dissolvedSPVs={spvs.filter(spv => spv.status === "DISSOLVED").length}
        organizationCount={organizations.length}
      />

      {/* SPV Table */}
      <SPVDataTable
        spvs={spvs}
        pagination={pagination}
        filters={filters}
        onSort={handleSort}
        onPageChange={handlePageChange}
        onView={handleViewSPV}
        showOrganization={true}
        showActions={true}
        isLoading={isLoading}
      />

      {/* Create SPV Form */}
      <EnhancedSPVCreationForm
        isOpen={isCreateFormOpen}
        onClose={() => setIsCreateFormOpen(false)}
        onSubmit={handleCreateSPV}
        organizations={organizations}
        mode="admin"
        isSubmitting={isCreating}
      />



      {/* Edit SPV Form */}
      {selectedSPV && (
        <AdminSPVForm
          isOpen={isEditFormOpen}
          onClose={() => {
            setIsEditFormOpen(false);
            setSelectedSPV(null);
          }}
          onSubmit={handleUpdateSPV}
          organizations={organizations}
          initialData={{
            ...selectedSPV,
            establishedDate: selectedSPV.establishedDate || "",
          }}
          mode="edit"
          isSubmitting={isUpdating}
        />
      )}

      {/* SPV Details View */}
      {detailsSPV && (
        <SPVDetailsView
          spv={detailsSPV}
          isOpen={isDetailsOpen}
          onClose={() => {
            setIsDetailsOpen(false);
            setDetailsSPVId(null);
          }}
          onEdit={() => {
            setSelectedSPV(detailsSPV);
            setIsDetailsOpen(false);
            setIsEditFormOpen(true);
          }}
          onDelete={() => {
            handleDeleteSPV(detailsSPV);
            setIsDetailsOpen(false);
          }}
          showActions={true}
          canVerifyDocuments={true}
          onDocumentVerify={handleDocumentVerification}
          onVerificationAction={handleSPVVerificationAction}
        />
      )}
    </div>
  );
}
