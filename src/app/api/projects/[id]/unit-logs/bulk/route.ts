import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantIsolation } from "@/lib/tenant-isolation";
import { z } from "zod";
import { ProjectAuditService } from "@/lib/audit/project-audit";
import { determineVerificationStatus } from "@/lib/utils/verification-status";

// Schema for individual unit log entry
const unitLogEntrySchema = z.object({
  logDate: z.string().datetime(),
  frequency: z.enum(["REAL_TIME", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]).default("MONTHLY"),
  unitType: z.string().min(1),
  quantity: z.number().positive(),
  dataSource: z.enum(["MANUAL", "CSV_UPLOAD", "API_INTEGRATION", "IOT_DEVICE"]).default("CSV_UPLOAD"),
  sourceFile: z.string().optional(),
  apiSource: z.string().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for bulk unit log creation
const bulkCreateUnitLogSchema = z.object({
  entries: z.array(unitLogEntrySchema).min(1).max(10000), // Limit to 10000 entries per batch
  validateOnly: z.boolean().default(false), // Option to validate without saving
});

interface BulkValidationResult {
  isValid: boolean;
  totalEntries: number;
  validEntries: number;
  errors: Array<{
    index: number;
    field: string;
    message: string;
    value?: any;
  }>;
  warnings: Array<{
    index: number;
    field: string;
    message: string;
    value?: any;
  }>;
}

/**
 * Validate bulk unit log entries
 */
function validateBulkEntries(entries: any[]): BulkValidationResult {
  const errors: BulkValidationResult['errors'] = [];
  const warnings: BulkValidationResult['warnings'] = [];
  let validEntries = 0;

  entries.forEach((entry, index) => {
    try {
      // Validate individual entry
      unitLogEntrySchema.parse(entry);
      
      // Additional business logic validation
      const logDate = new Date(entry.logDate);
      const now = new Date();
      
      // Check if date is in the future
      if (logDate > now) {
        warnings.push({
          index,
          field: 'logDate',
          message: 'Log date is in the future',
          value: entry.logDate
        });
      }
      
      // Check if date is too far in the past (more than 10 years)
      const tenYearsAgo = new Date();
      tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
      if (logDate < tenYearsAgo) {
        warnings.push({
          index,
          field: 'logDate',
          message: 'Log date is more than 10 years old',
          value: entry.logDate
        });
      }
      
      // Check for extremely high quantities that might be data entry errors
      if (entry.quantity > 1000000) {
        warnings.push({
          index,
          field: 'quantity',
          message: 'Quantity seems unusually high, please verify',
          value: entry.quantity
        });
      }
      
      validEntries++;
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach(zodError => {
          errors.push({
            index,
            field: zodError.path.join('.'),
            message: zodError.message,
            value: zodError.code === 'invalid_type' ? entry[zodError.path[0]] : undefined
          });
        });
      } else {
        errors.push({
          index,
          field: 'general',
          message: 'Unknown validation error',
        });
      }
    }
  });

  return {
    isValid: errors.length === 0,
    totalEntries: entries.length,
    validEntries,
    errors,
    warnings,
  };
}

/**
 * Create multiple unit log entries in bulk
 */
async function bulkCreateUnitLogsHandler(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session?.user?.id) {
    throw new ApiError(
      "Unauthorized - Please log in",
      ErrorType.UNAUTHORIZED,
      401
    );
  }

  const userId = session.user.id;
  logger.info("Bulk creating unit logs", { userId, projectId: params.id });

  // Determine appropriate verification status based on user role
  const verificationStatus = await determineVerificationStatus(userId, params.id);
  logger.info(`Determined verification status: ${verificationStatus} for user ${userId}`);

  try {
    // Check project access with proper authorization
    let project = null;

    // First, try to find project by organization access (for regular org users)
    if (session.user.organizationId) {
      project = await db.project.findFirst({
        where: {
          id: params.id,
          organizationId: session.user.organizationId
        }
      });
    }

    // If not found and user is SPV user, check SPV access
    if (!project && session.user.role === "SPV_USER") {
      const spvUserAccess = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          isActive: true,
          projectAssignments: {
            some: {
              projectId: params.id,
              isActive: true
            }
          }
        },
        include: {
          spv: {
            include: {
              projects: {
                where: { id: params.id }
              }
            }
          }
        }
      });

      if (spvUserAccess && spvUserAccess.spv.projects.length > 0) {
        project = spvUserAccess.spv.projects[0];
      }
    }

    // If still not found and user is admin, allow access to any project
    if (!project && session.user.role === "ADMIN") {
      project = await db.project.findUnique({
        where: { id: params.id }
      });
    }

    if (!project) {
      throw new ApiError(
        "Project not found or access denied",
        ErrorType.NOT_FOUND,
        404
      );
    }

    console.log("Bulk upload: Project found:", project.name);

    // Parse request body
    const body = await req.json();
    console.log("Bulk upload request body:", JSON.stringify(body, null, 2));
    console.log("Number of entries:", body.entries?.length);

    // Log first entry for debugging
    if (body.entries && body.entries.length > 0) {
      console.log("First entry:", JSON.stringify(body.entries[0], null, 2));
    }

    const { entries, validateOnly } = bulkCreateUnitLogSchema.parse(body);

    // Validate all entries
    const validationResult = validateBulkEntries(entries);

    // If validation only, return validation results
    if (validateOnly) {
      return NextResponse.json({
        validation: validationResult,
        message: validationResult.isValid 
          ? "All entries are valid" 
          : `Found ${validationResult.errors.length} validation errors`,
      });
    }

    // If there are validation errors, don't proceed with creation
    if (!validationResult.isValid) {
      throw new ApiError(
        "Validation failed",
        ErrorType.VALIDATION,
        400,
        validationResult.errors
      );
    }

    // Check for duplicate entries within the batch
    // For hybrid projects, include generation type in the key to allow multiple entries per date
    const duplicateCheck = new Map<string, number[]>();
    entries.forEach((entry, index) => {
      // Create a unique key that includes generation type for hybrid projects
      const generationType = entry.metadata?.generationType || 'standard';
      const key = `${entry.logDate}-${entry.unitType}-${generationType}`;

      if (!duplicateCheck.has(key)) {
        duplicateCheck.set(key, []);
      }
      duplicateCheck.get(key)!.push(index);
    });

    const duplicates = Array.from(duplicateCheck.entries())
      .filter(([_, indices]) => indices.length > 1)
      .map(([key, indices]) => ({ key, indices }));

    if (duplicates.length > 0) {
      // Create more user-friendly error messages
      const errorDetails = duplicates.map(dup => {
        const [logDate, unitType, generationType] = dup.key.split('-');
        const readableDate = new Date(logDate).toLocaleDateString();

        if (generationType && generationType !== 'standard') {
          return {
            message: `Duplicate ${generationType.toLowerCase()} generation entry found for ${readableDate}. Please ensure each date appears only once in your CSV file.`,
            indices: dup.indices,
            date: readableDate,
            generationType: generationType
          };
        } else {
          return {
            message: `Duplicate entry found for ${readableDate} (${unitType}). Please ensure each date appears only once in your CSV file.`,
            indices: dup.indices,
            date: readableDate,
            unitType: unitType
          };
        }
      });

      throw new ApiError(
        "Duplicate entries found in batch. Please check your CSV file for duplicate dates.",
        ErrorType.VALIDATION,
        400,
        errorDetails
      );
    }

    // Check for existing entries in the database
    // For hybrid projects, we need to check metadata.generationType as well
    const existingEntries = await db.unitLog.findMany({
      where: {
        projectId: params.id,
        OR: entries.map(entry => ({
          logDate: new Date(entry.logDate),
          unitType: entry.unitType,
          // For hybrid projects, also check generation type in metadata
          ...(entry.metadata?.generationType && {
            metadata: {
              path: ["generationType"],
              equals: entry.metadata.generationType
            }
          })
        })),
      },
      select: {
        logDate: true,
        unitType: true,
        metadata: true,
      },
    });

    if (existingEntries.length > 0) {
      const existingKeys = existingEntries.map(entry => {
        const generationType = (entry.metadata as any)?.generationType || 'standard';
        return `${entry.logDate.toISOString()}-${entry.unitType}-${generationType}`;
      });

      const conflictingEntries = entries
        .map((entry, index) => ({ entry, index }))
        .filter(({ entry }) => {
          const generationType = entry.metadata?.generationType || 'standard';
          const entryKey = `${new Date(entry.logDate).toISOString()}-${entry.unitType}-${generationType}`;
          return existingKeys.includes(entryKey);
        });

      if (conflictingEntries.length > 0) {
        throw new ApiError(
          "Some entries already exist",
          ErrorType.CONFLICT,
          409,
          conflictingEntries.map(({ entry, index }) => {
            const generationType = entry.metadata?.generationType || 'standard';
            return {
              index,
              message: `Entry for ${entry.logDate} (${entry.unitType}${generationType !== 'standard' ? ` - ${generationType}` : ''}) already exists`,
            };
          })
        );
      }
    }

    // Create all entries in a transaction
    console.log("Starting database transaction for", entries.length, "entries");

    const createdEntries = await db.$transaction(async (tx) => {
      const results = [];

      for (let i = 0; i < entries.length; i++) {
        const entry = entries[i];
        console.log(`Creating entry ${i + 1}/${entries.length}:`, {
          logDate: entry.logDate,
          unitType: entry.unitType,
          quantity: entry.quantity,
          frequency: entry.frequency,
          dataSource: entry.dataSource
        });

        try {
          const unitLog = await tx.unitLog.create({
            data: {
              logDate: new Date(entry.logDate),
              frequency: entry.frequency as any,
              unitType: entry.unitType,
              quantity: entry.quantity,
              dataSource: entry.dataSource as any,
              sourceFile: entry.sourceFile,
              apiSource: entry.apiSource,
              notes: entry.notes,
              metadata: entry.metadata as any,
              projectId: params.id,
              loggedBy: userId,
              verificationStatus,
            },
            include: {
              logger: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });

          results.push(unitLog);
          console.log(`Successfully created entry ${i + 1}`);
        } catch (entryError) {
          console.error(`Error creating entry ${i + 1}:`, entryError);
          throw entryError;
        }
      }

      return results;
    });

    logger.info(
      `User ${userId} bulk created ${createdEntries.length} unit logs for project ${params.id}`
    );

    // Log audit event for bulk upload
    await ProjectAuditService.logProjectActivity({
      type: "DOCUMENT_UPLOADED", // Using existing type until new types are migrated
      description: `Bulk CSV upload: ${createdEntries.length} monitoring data entries created`,
      projectId: params.id,
      userId: userId,
      organizationId: project.organizationId,
      metadata: {
        totalEntries: createdEntries.length,
        dataSource: "CSV_UPLOAD",
        sourceFile: entries[0]?.sourceFile || "bulk_upload.csv",
        entryIds: createdEntries.map(entry => entry.id),
      },
      ipAddress: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
      userAgent: req.headers.get('user-agent') || undefined,
    });

    return NextResponse.json({
      unitLogs: createdEntries,
      validation: validationResult,
      summary: {
        totalCreated: createdEntries.length,
        totalRequested: entries.length,
        hasWarnings: validationResult.warnings.length > 0,
      },
      message: `Successfully created ${createdEntries.length} unit log entries`,
    });

  } catch (error) {
    console.error("Bulk upload error details:", error);

    if (error instanceof z.ZodError) {
      console.error("Zod validation error:", error.errors);
      throw new ApiError(
        "Invalid request data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    if (error instanceof ApiError) {
      console.error("API error:", error.message);
      throw error;
    }

    console.error("Unexpected error:", error);
    logger.error(`Error bulk creating unit logs for project ${params.id}:`, error);
    throw new ApiError(
      "An error occurred while creating unit logs",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling
const wrappedPostHandler = withErrorHandling(bulkCreateUnitLogsHandler);

// Export the handler with resource isolation middleware
export const POST = withResourceIsolation('project', 'id')(wrappedPostHandler);
