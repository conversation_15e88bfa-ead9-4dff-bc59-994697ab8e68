import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for SPV updates - Updated to match edit form fields
const spvUpdateSchema = z.object({
  // Basic Information
  name: z.string().min(1, "SPV name is required").optional(),
  purpose: z.string().optional(),
  legalStructure: z.string().optional(),
  projectCategories: z.string().optional(),
  adminName: z.string().optional(),
  adminEmail: z.string().optional(),

  // Legal & Compliance
  jurisdiction: z.string().optional(),
  country: z.string().optional(),
  gstNumber: z.string().optional(),
  cinNumber: z.string().optional(),
  panNumber: z.string().optional(),
  incorporationDate: z.string().optional(),
  registeredAddress: z.string().optional(),
  description: z.string().optional(),

  // Legacy fields (for backward compatibility)
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: z.string().optional(),
  legalEntityId: z.string().optional(),
  contact: z.string().optional(),
});

/**
 * GET /api/organizations/spvs/[id]
 * Get a specific SPV with detailed information for the organization
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    if (!spvId) {
      return NextResponse.json(
        { error: "SPV ID is required" },
        { status: 400 }
      );
    }

    // Build where clause based on user role
    const whereClause: any = { id: spvId };

    // For organization admins, restrict to their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (!session.user.organizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization ID" },
          { status: 400 }
        );
      }
      whereClause.organizationId = session.user.organizationId;
    }
    // Platform admins can access any SPV

    // Get the SPV with detailed information
    const spv = await db.sPV.findFirst({
      where: whereClause,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
            status: true,
            verificationStatus: true,
            country: true,
            industry: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            type: true,
            createdAt: true,
            estimatedReductions: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        spvUsers: {
          where: {
            isActive: true,
          },
          select: {
            id: true,
            role: true,
            isActive: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        documents: {
          select: {
            id: true,
            documentType: true,
            fileName: true,
            fileUrl: true,
            fileSize: true,
            mimeType: true,
            uploadedBy: true,
            uploadedAt: true,
            verified: true,
            verifiedBy: true,
            verifiedAt: true,
            notes: true,
            createdAt: true,
            updatedAt: true,
            uploader: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            verifier: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            uploadedAt: "desc",
          },
        },
        _count: {
          select: {
            projects: true,
            spvUsers: true,
          },
        },
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    logger.info("SPV details fetched for organization admin", {
      userId: session.user.id,
      spvId: spvId,
      organizationId: session.user.organizationId,
    });

    return NextResponse.json({ spv });

  } catch (error) {
    logger.error("Error fetching SPV details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/organizations/spvs/[id]
 * Update SPV details
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;
    const body = await request.json();

    console.log("PUT /api/organizations/spvs/[id] - Request data:", {
      spvId,
      body,
      userRole: session.user.role,
      organizationId: session.user.organizationId
    });

    const validatedData = spvUpdateSchema.parse(body);

    // Build where clause based on user role
    const whereClause: any = { id: spvId };

    // For organization admins, restrict to their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (!session.user.organizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization ID" },
          { status: 400 }
        );
      }
      whereClause.organizationId = session.user.organizationId;
    }

    // Check if SPV exists and user has access
    const existingSPV = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!existingSPV) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Update the SPV
    const updatedSPV = await db.sPV.update({
      where: { id: spvId },
      data: {
        // Basic Information
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.purpose !== undefined && { purpose: validatedData.purpose }),
        ...(validatedData.legalStructure !== undefined && { legalStructure: validatedData.legalStructure }),
        ...(validatedData.projectCategories && { projectCategories: [validatedData.projectCategories] }),

        // Legal & Compliance
        ...(validatedData.jurisdiction !== undefined && { jurisdiction: validatedData.jurisdiction }),
        ...(validatedData.country !== undefined && { country: validatedData.country }),
        ...(validatedData.gstNumber !== undefined && { gstNumber: validatedData.gstNumber }),
        ...(validatedData.cinNumber !== undefined && { cinNumber: validatedData.cinNumber }),
        ...(validatedData.panNumber !== undefined && { panNumber: validatedData.panNumber }),
        ...(validatedData.incorporationDate !== undefined && {
          incorporationDate: validatedData.incorporationDate ? new Date(validatedData.incorporationDate) : null
        }),
        ...(validatedData.registeredAddress !== undefined && { registeredAddress: validatedData.registeredAddress }),
        ...(validatedData.description !== undefined && { description: validatedData.description }),

        // Legacy fields (for backward compatibility)
        ...(validatedData.registrationNumber !== undefined && { registrationNumber: validatedData.registrationNumber }),
        ...(validatedData.taxId !== undefined && { taxId: validatedData.taxId }),
        ...(validatedData.address !== undefined && { address: validatedData.address }),
        ...(validatedData.legalEntityId !== undefined && { legalEntityId: validatedData.legalEntityId }),
        ...(validatedData.contact !== undefined && { contact: validatedData.contact }),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
      },
    });

    // Update admin user if admin fields are provided
    if (validatedData.adminName !== undefined || validatedData.adminEmail !== undefined) {
      try {
        // Find the SPV admin user
        const spvAdmin = await db.sPVUser.findFirst({
          where: {
            spvId: spvId,
            role: "SPV_ADMIN"
          },
          include: {
            user: true
          }
        });

        if (spvAdmin) {
          // Update the admin user
          await db.user.update({
            where: { id: spvAdmin.user.id },
            data: {
              ...(validatedData.adminName !== undefined && { name: validatedData.adminName }),
              ...(validatedData.adminEmail !== undefined && { email: validatedData.adminEmail }),
            }
          });
        }
      } catch (adminUpdateError) {
        // Log the error but don't fail the SPV update
        logger.warn(`Failed to update admin user for SPV ${spvId}:`, adminUpdateError);
      }
    }

    logger.info(`SPV ${spvId} updated by user ${session.user.id}`);

    return NextResponse.json({
      message: "SPV updated successfully",
      spv: updatedSPV,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error updating SPV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
