import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

// Validation schema for document upload
const documentUploadSchema = z.object({
  documentType: z.enum([
    "CERTIFICATE_OF_INCORPORATION",
    "PAN_CARD",
    "GST_REGISTRATION",
    "BOARD_RESOLUTION",
    "BANK_PROOF",
    "AUTHORIZED_SIGNATORY_ID",
    "MOA_AOA",
    "OTHER"
  ], {
    required_error: "Document type is required",
  }),
  fileName: z.string().min(1, "File name is required"),
  fileUrl: z.string().url("Valid file URL is required"),
  fileSize: z.number().optional(),
  mimeType: z.string().optional(),
  notes: z.string().optional(),
});

/**
 * GET /api/organizations/spvs/[id]/documents
 * Get all documents for an SPV
 */
async function getSPVDocumentsHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access SPV documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    const spvId = params.id;

    // Get SPV to check permissions
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      select: {
        id: true,
        organizationId: true,
        verificationStatus: true,
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    // Check authorization
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" && 
                      session.user.organizationId === spv.organizationId;
    const isPlatformAdmin = session.user.role === "ADMIN";
    const isSPVUser = session.user.role === "SPV_USER";

    if (!isOrgAdmin && !isPlatformAdmin && !isSPVUser) {
      throw new ApiError(
        "You don't have permission to access SPV documents",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // If SPV user, check if they belong to this SPV
    if (isSPVUser) {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          spvId: spvId,
          isActive: true,
        },
      });

      if (!spvUser) {
        throw new ApiError(
          "You don't have access to this SPV",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Get documents
    const documents = await db.sPVDocument.findMany({
      where: { spvId },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        uploadedAt: 'desc',
      },
    });

    return NextResponse.json({
      documents,
      canUpload: isOrgAdmin || isSPVUser,
      canVerify: isPlatformAdmin,
    });
  } catch (error) {
    logger.error("Error fetching SPV documents:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while fetching SPV documents",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * POST /api/organizations/spvs/[id]/documents
 * Upload a new document for SPV verification
 */
async function uploadSPVDocumentHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to upload SPV documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    const spvId = params.id;
    const body = await request.json();
    const validatedData = documentUploadSchema.parse(body);

    // Get SPV to check permissions
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      select: {
        id: true,
        organizationId: true,
        verificationStatus: true,
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    // Check authorization
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" && 
                      session.user.organizationId === spv.organizationId;
    const isSPVUser = session.user.role === "SPV_USER";

    if (!isOrgAdmin && !isSPVUser) {
      throw new ApiError(
        "You don't have permission to upload documents for this SPV",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // If SPV user, check if they belong to this SPV
    if (isSPVUser) {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          spvId: spvId,
          isActive: true,
        },
      });

      if (!spvUser) {
        throw new ApiError(
          "You don't have access to this SPV",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Check if document type already exists (replace if it does)
    const existingDocument = await db.sPVDocument.findFirst({
      where: {
        spvId,
        documentType: validatedData.documentType,
      },
    });

    let document;

    if (existingDocument) {
      // Update existing document
      document = await db.sPVDocument.update({
        where: { id: existingDocument.id },
        data: {
          fileName: validatedData.fileName,
          fileUrl: validatedData.fileUrl,
          fileSize: validatedData.fileSize || null,
          mimeType: validatedData.mimeType || null,
          notes: validatedData.notes || null,
          uploadedBy: session.user.id,
          uploadedAt: new Date(),
          verified: false, // Reset verification status
          verifiedBy: null,
          verifiedAt: null,
          updatedAt: new Date(),
        },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    } else {
      // Create new document
      document = await db.sPVDocument.create({
        data: {
          spvId,
          documentType: validatedData.documentType,
          fileName: validatedData.fileName,
          fileUrl: validatedData.fileUrl,
          fileSize: validatedData.fileSize || null,
          mimeType: validatedData.mimeType || null,
          notes: validatedData.notes || null,
          uploadedBy: session.user.id,
        },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
    }

    logger.info(`SPV document uploaded: ${document.id} for SPV ${spvId} by user ${session.user.id}`);

    return NextResponse.json({
      document,
      message: existingDocument 
        ? "Document updated successfully" 
        : "Document uploaded successfully",
    });
  } catch (error) {
    logger.error("Error uploading SPV document:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      logger.error("Zod validation error:", error);
      throw new ApiError(
        `Invalid document data provided: ${error.message}`,
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while uploading the document",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getSPVDocumentsHandler);
export const POST = withErrorHandling(uploadSPVDocumentHandler);
