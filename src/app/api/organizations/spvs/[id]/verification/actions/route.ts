import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";
import { emailService } from "@/lib/email";

// Validation schema for verification actions
const verificationActionSchema = z.object({
  action: z.enum(["APPROVE", "REJECT", "REQUEST_MORE_INFO"], {
    required_error: "Action is required",
  }),
  notes: z.string().optional(),
  rejectionReason: z.string().optional(),
});

type VerificationAction = z.infer<typeof verificationActionSchema>;

/**
 * POST /api/organizations/spvs/[id]/verification/actions
 * Perform verification actions on SPV (approve, reject, request more info)
 */
async function performVerificationActionHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to perform verification actions",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Only platform admins and super admins can perform verification actions
    if (session.user.role !== "ADMIN" && session.user.role !== "SUPER_ADMIN") {
      throw new ApiError(
        "You don't have permission to perform verification actions",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const spvId = params.id;
    const body = await request.json();
    const validatedData = verificationActionSchema.parse(body);

    // Get SPV with organization details
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spvUsers: {
          where: {
            role: "SPV_ADMIN",
            isActive: true,
          },
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    // Check if SPV is in a state that allows verification actions
    if (!["IN_REVIEW", "PENDING_VERIFICATION", "NEEDS_MORE_INFO"].includes(spv.verificationStatus)) {
      throw new ApiError(
        "SPV is not in a state that allows verification actions",
        ErrorType.VALIDATION,
        400
      );
    }

    // Determine new status based on action
    let newStatus: string;
    let verifiedAt: Date | null = null;
    let rejectionReason: string | null = null;

    switch (validatedData.action) {
      case "APPROVE":
        newStatus = "VERIFIED";
        verifiedAt = new Date();
        break;
      case "REJECT":
        newStatus = "REJECTED";
        rejectionReason = validatedData.rejectionReason || "SPV verification rejected";
        break;
      case "REQUEST_MORE_INFO":
        newStatus = "NEEDS_MORE_INFO";
        break;
      default:
        throw new ApiError("Invalid verification action", ErrorType.VALIDATION, 400);
    }

    // Update SPV verification status
    const updatedSPV = await db.sPV.update({
      where: { id: spvId },
      data: {
        verificationStatus: newStatus as any,
        verificationNotes: validatedData.notes || null,
        verifiedBy: validatedData.action === "APPROVE" ? session.user.id : null,
        verifiedAt: verifiedAt,
        rejectionReason: rejectionReason,
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
      },
    });

    logger.info(`SPV verification action performed: ${spvId} - ${validatedData.action} by admin ${session.user.id}`);

    // Send notification emails
    try {
      const spvAdminEmails = spv.spvUsers.map(spvUser => spvUser.user.email);
      const organizationAdminEmails = await db.user.findMany({
        where: {
          organizationId: spv.organizationId,
          role: "ORGANIZATION_ADMIN",
        },
        select: {
          email: true,
          name: true,
        },
      });

      const allNotificationEmails = [
        ...spvAdminEmails,
        ...organizationAdminEmails.map(admin => admin.email),
      ];

      // Remove duplicates
      const uniqueEmails = [...new Set(allNotificationEmails)];

      for (const email of uniqueEmails) {
        await emailService.sendSPVVerificationStatusEmail(
          email,
          spv.name,
          spv.organization.name || spv.organization.legalName || "Organization",
          newStatus,
          validatedData.notes,
          rejectionReason
        );
      }

      logger.info(`SPV verification status emails sent to ${uniqueEmails.length} recipients`);
    } catch (emailError) {
      logger.error(`Failed to send SPV verification status emails:`, emailError);
      // Don't fail the entire operation if email fails
    }

    // Prepare response message
    let message: string;
    switch (validatedData.action) {
      case "APPROVE":
        message = `SPV "${spv.name}" has been verified and approved successfully`;
        break;
      case "REJECT":
        message = `SPV "${spv.name}" verification has been rejected`;
        break;
      case "REQUEST_MORE_INFO":
        message = `More information has been requested for SPV "${spv.name}"`;
        break;
      default:
        message = "Verification action completed";
    }

    return NextResponse.json({
      spv: updatedSPV,
      message,
      action: validatedData.action,
      newStatus,
    });
  } catch (error) {
    logger.error("Error performing SPV verification action:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      throw new ApiError(
        "Invalid verification action data provided",
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while performing verification action",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(performVerificationActionHandler);
