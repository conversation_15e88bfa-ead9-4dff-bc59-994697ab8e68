"use client";

import { useState } from "react";
import { Search, Filter, X, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { SPVStatus, SPVQueryParams } from "@/types/spv";

interface SPVFiltersProps {
  filters: {
    search?: string;
    organizationId?: string;
    status?: SPVStatus;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
  organizations?: {
    id: string;
    name: string;
    legalName: string | null;
  }[];
  onFiltersChange: (filters: Partial<SPVQueryParams>) => void;
  showOrganizationFilter?: boolean;
}

export function SPVFilters({
  filters,
  organizations = [],
  onFiltersChange,
  showOrganizationFilter = false,
}: SPVFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || "");

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onFiltersChange({ search: value });
  };

  const handleOrganizationFilter = (organizationId: string) => {
    onFiltersChange({ 
      organizationId: organizationId === "all" ? undefined : organizationId 
    });
  };

  const handleStatusFilter = (status: string) => {
    onFiltersChange({ 
      status: status === "all" ? undefined : status as SPVStatus 
    });
  };

  const clearFilters = () => {
    setSearchTerm("");
    onFiltersChange({
      search: undefined,
      organizationId: undefined,
      status: undefined,
    });
  };

  const hasActiveFilters = filters.search || filters.organizationId || filters.status;

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex gap-4 flex-wrap">
        {/* Search Input */}
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search SPVs..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
            autoComplete="off"
          />
        </div>
        
        {/* Organization Filter */}
        {showOrganizationFilter && organizations.length > 0 && (
          <Select 
            value={filters.organizationId || "all"} 
            onValueChange={handleOrganizationFilter}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Organizations" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Organizations</SelectItem>
              {organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Status Filter */}
        <Select 
          value={filters.status || "all"} 
          onValueChange={handleStatusFilter}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="ACTIVE">Active</SelectItem>
            <SelectItem value="INACTIVE">Inactive</SelectItem>
            <SelectItem value="DISSOLVED">Dissolved</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: {filters.search}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleSearchChange("")}
              />
            </Badge>
          )}
          
          {filters.organizationId && showOrganizationFilter && (
            <Badge variant="secondary" className="gap-1">
              Organization: {organizations.find(org => org.id === filters.organizationId)?.name || "Unknown"}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleOrganizationFilter("all")}
              />
            </Badge>
          )}
          
          {filters.status && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.status}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleStatusFilter("all")}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}

// SPV Statistics Component
interface SPVStatsProps {
  totalSPVs: number;
  activeSPVs: number;
  pendingSPVs: number;
  inactiveSPVs: number;
  dissolvedSPVs?: number;
  organizationCount?: number;
  // Verification status counts
  verifiedSPVs?: number;
  pendingVerificationSPVs?: number;
  rejectedSPVs?: number;
  inReviewSPVs?: number;
}

export function SPVStats({
  totalSPVs,
  activeSPVs,
  pendingSPVs,
  inactiveSPVs,
  dissolvedSPVs,
  organizationCount,
  verifiedSPVs,
  pendingVerificationSPVs,
  rejectedSPVs,
  inReviewSPVs,
}: SPVStatsProps) {
  return (
    <div className="space-y-6">
      {/* SPV Status Stats */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-3">SPV Status Overview</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total SPVs</p>
                <p className="text-2xl font-bold">{totalSPVs}</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Filter className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active SPVs</p>
                <p className="text-2xl font-bold text-green-600">{activeSPVs}</p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </div>

          {/* Pending SPVs badge commented out for now */}
          {/* <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending SPVs</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingSPVs}</p>
              </div>
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </div> */}

          <div className="bg-white p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Inactive SPVs</p>
                <p className="text-2xl font-bold text-gray-600">{inactiveSPVs}</p>
              </div>
              <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                <X className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Verification Status Stats - Commented out for now */}
      {/*
      {(verifiedSPVs !== undefined || pendingVerificationSPVs !== undefined || rejectedSPVs !== undefined || inReviewSPVs !== undefined) && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-3">Verification Status Overview</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {verifiedSPVs !== undefined && (
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Verified</p>
                    <p className="text-2xl font-bold text-green-600">{verifiedSPVs}</p>
                  </div>
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Shield className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </div>
            )}

            {pendingVerificationSPVs !== undefined && (
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Pending Verification</p>
                    <p className="text-2xl font-bold text-gray-600">{pendingVerificationSPVs}</p>
                  </div>
                  <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Clock className="h-4 w-4 text-gray-600" />
                  </div>
                </div>
              </div>
            )}

            {inReviewSPVs !== undefined && (
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">In Review</p>
                    <p className="text-2xl font-bold text-blue-600">{inReviewSPVs}</p>
                  </div>
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Eye className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              </div>
            )}

            {rejectedSPVs !== undefined && (
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                    <p className="text-2xl font-bold text-red-600">{rejectedSPVs}</p>
                  </div>
                  <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      */}
    </div>
  );
}
