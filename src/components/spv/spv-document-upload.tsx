"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { z } from "zod";
import { Upload, File, X, Check, AlertCircle, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { SPVDocumentType } from "@/types/spv";
import { toast } from "sonner";

const documentTypes = [
  { value: "CERTIFICATE_OF_INCORPORATION", label: "Certificate of Incorporation", required: true },
  { value: "PAN_CARD", label: "PAN Card", required: true },
  { value: "GST_REGISTRATION", label: "GST Registration Certificate", required: true },
  { value: "BOARD_RESOLUTION", label: "Board Resolution", required: true },
  { value: "BANK_PROOF", label: "Bank Account Proof", required: true },
  { value: "AUTHORIZED_SIGNATORY_ID", label: "Authorized Signatory ID Proof", required: true },
  { value: "MOA_AOA", label: "MOA & AOA", required: true },
  { value: "OTHER", label: "Other Document", required: false },
];

interface DocumentUpload {
  file: File;
  documentType: SPVDocumentType;
  notes?: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

interface SPVDocumentUploadProps {
  spvId: string;
  onUploadComplete: (document: any) => void;
  existingDocuments?: Array<{
    id: string;
    documentType: SPVDocumentType;
    fileName: string;
    verified: boolean;
  }>;
  preSelectedDocumentType?: SPVDocumentType;
}

export function SPVDocumentUpload({
  spvId,
  onUploadComplete,
  existingDocuments = [],
  preSelectedDocumentType,
}: SPVDocumentUploadProps) {
  const [uploads, setUploads] = useState<DocumentUpload[]>([]);
  const [selectedType, setSelectedType] = useState<SPVDocumentType | "">(preSelectedDocumentType || "");
  const [notes, setNotes] = useState("");

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (!selectedType) {
      toast.error("Please select a document type first");
      return;
    }

    const newUploads = acceptedFiles.map(file => ({
      file,
      documentType: selectedType as SPVDocumentType,
      notes,
      progress: 0,
      status: 'pending' as const,
    }));

    setUploads(prev => [...prev, ...newUploads]);
    setNotes("");
  }, [selectedType, notes]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: !selectedType,
  });

  const uploadDocument = async (upload: DocumentUpload, index: number) => {
    try {
      setUploads(prev => prev.map((u, i) => 
        i === index ? { ...u, status: 'uploading', progress: 0 } : u
      ));

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploads(prev => prev.map((u, i) => 
          i === index && u.progress < 90 
            ? { ...u, progress: u.progress + 10 } 
            : u
        ));
      }, 200);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', upload.file);
      formData.append('documentType', upload.documentType);
      if (upload.notes) {
        formData.append('notes', upload.notes);
      }

      // Upload to your file storage service (e.g., AWS S3, Cloudinary, etc.)
      // This is a placeholder - replace with your actual upload logic
      console.log('Uploading file:', upload.file.name, 'Size:', upload.file.size, 'Type:', upload.file.type);

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      console.log('Upload response status:', uploadResponse.status);

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error('Upload failed:', errorText);
        throw new Error(`Upload failed: ${errorText}`);
      }

      const uploadResult = await uploadResponse.json();
      console.log('Upload result:', uploadResult);

      clearInterval(progressInterval);

      // Update progress to 100%
      setUploads(prev => prev.map((u, i) => 
        i === index ? { ...u, progress: 100 } : u
      ));

      // Save document metadata to database
      const documentData = {
        documentType: selectedType,
        fileName: upload.file.name,
        fileUrl: uploadResult.url,
        fileSize: upload.file.size,
        mimeType: upload.file.type,
        notes: upload.notes,
      };

      console.log('Saving document metadata:', documentData);

      const documentResponse = await fetch(`/api/organizations/spvs/${spvId}/documents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(documentData),
      });

      if (!documentResponse.ok) {
        const errorData = await documentResponse.json().catch(() => ({}));
        console.error('Document save error:', errorData);
        throw new Error(errorData.error || 'Failed to save document metadata');
      }

      const documentResult = await documentResponse.json();

      setUploads(prev => prev.map((u, i) => 
        i === index ? { ...u, status: 'success' } : u
      ));

      onUploadComplete(documentResult.document);
      toast.success(`${upload.file.name} uploaded successfully`);

    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploads(prev => prev.map((u, i) =>
        i === index ? {
          ...u,
          status: 'error',
          error: errorMessage
        } : u
      ));
      toast.error(`Failed to upload ${upload.file.name}: ${errorMessage}`);
    }
  };

  const removeUpload = (index: number) => {
    setUploads(prev => prev.filter((_, i) => i !== index));
  };

  const startUpload = (index: number) => {
    const upload = uploads[index];
    if (upload.status === 'pending') {
      uploadDocument(upload, index);
    }
  };

  const getDocumentTypeStatus = (type: SPVDocumentType) => {
    const existing = existingDocuments.find(doc => doc.documentType === type);
    if (existing) {
      return existing.verified ? 'verified' : 'uploaded';
    }
    return 'missing';
  };

  const requiredDocuments = documentTypes.filter(doc => doc.required);
  const uploadedCount = requiredDocuments.filter(doc =>
    existingDocuments.some(existing => existing.documentType === doc.value)
  ).length;

  return (
    <div className="space-y-6">
      {/* Upload Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Documents</CardTitle>
  
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Document Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="documentType">Document Type *</Label>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map(docType => (
                  <SelectItem key={docType.value} value={docType.value}>
                    <div className="flex items-center gap-2">
                      {docType.label}
                      {docType.required && <Badge variant="outline" className="text-xs">Required</Badge>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this document"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[60px]"
            />
          </div>

          {/* Drop Zone */}
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
              ${!selectedType ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
            `}
          >
            <input {...getInputProps()} />
            <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
            {isDragActive ? (
              <p>Drop the files here...</p>
            ) : (
              <div>
                <p className="text-sm font-medium mb-1">
                  {selectedType ? 'Click to upload or drag and drop' : 'Select a document type first'}
                </p>
                <p className="text-xs text-muted-foreground">
                  PDF, DOC, DOCX, PNG, JPG up to 10MB
                </p>
              </div>
            )}
          </div>

          {/* Upload Queue */}
          {uploads.length > 0 && (
            <div className="space-y-2">
              <Label>Upload Queue</Label>
              {uploads.map((upload, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  <File className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{upload.file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {documentTypes.find(t => t.value === upload.documentType)?.label}
                    </p>
                    {upload.status === 'uploading' && (
                      <Progress value={upload.progress} className="mt-1" />
                    )}
                    {upload.status === 'error' && (
                      <p className="text-xs text-destructive mt-1">{upload.error}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {upload.status === 'pending' && (
                      <Button size="sm" onClick={() => startUpload(index)}>
                        Upload
                      </Button>
                    )}
                    {upload.status === 'success' && (
                      <Check className="h-4 w-4 text-green-600" />
                    )}
                    {upload.status === 'error' && (
                      <AlertCircle className="h-4 w-4 text-destructive" />
                    )}
                    {upload.status !== 'uploading' && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeUpload(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
