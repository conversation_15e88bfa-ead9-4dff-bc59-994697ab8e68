"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  File,
  Download,
  Eye,
  Check,
  X,
  Clock,
  User,
  FileText,
  Shield,
  AlertCircle,
  Upload
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { SPVDocument, SPVDocumentType } from "@/types/spv";
import { toast } from "sonner";

const documentTypeLabels: Record<SPVDocumentType, string> = {
  CERTIFICATE_OF_INCORPORATION: "Certificate of Incorporation",
  PAN_CARD: "PAN Card",
  GST_REGISTRATION: "GST Registration Certificate",
  BOARD_RESOLUTION: "Board Resolution",
  BANK_PROOF: "Bank Account Proof",
  AUTHORIZED_SIGNATORY_ID: "Authorized Signatory ID Proof",
  MOA_AOA: "MOA & AOA",
  OTHER: "Other Document",
};

interface SPVDocumentViewerProps {
  documents: SPVDocument[];
  canVerify?: boolean;
  canUpload?: boolean;
  onVerifyDocument?: (documentId: string, verified: boolean, notes?: string) => Promise<void>;
  onDeleteDocument?: (documentId: string) => Promise<void>;
  onUploadDocument?: (documentType: SPVDocumentType) => void;
  spvId?: string;
}

export function SPVDocumentViewer({
  documents,
  canVerify = false,
  canUpload = false,
  onVerifyDocument,
  onDeleteDocument,
  onUploadDocument,
  spvId,
}: SPVDocumentViewerProps) {
  const [verificationDialog, setVerificationDialog] = useState<{
    isOpen: boolean;
    document: SPVDocument | null;
    action: 'verify' | 'reject' | null;
  }>({
    isOpen: false,
    document: null,
    action: null,
  });
  const [verificationNotes, setVerificationNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleVerificationAction = async (action: 'verify' | 'reject') => {
    if (!verificationDialog.document || !onVerifyDocument) return;

    setIsSubmitting(true);
    try {
      await onVerifyDocument(
        verificationDialog.document.id,
        action === 'verify',
        verificationNotes || undefined
      );
      
      setVerificationDialog({ isOpen: false, document: null, action: null });
      setVerificationNotes("");
      toast.success(`Document ${action === 'verify' ? 'verified' : 'rejected'} successfully`);
    } catch (error) {
      console.error('Verification error:', error);
      toast.error(`Failed to ${action} document`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const openVerificationDialog = (document: SPVDocument, action: 'verify' | 'reject') => {
    setVerificationDialog({ isOpen: true, document, action });
    setVerificationNotes(document.notes || "");
  };

  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return "Unknown size";
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getDocumentIcon = (mimeType: string | null) => {
    if (!mimeType) return <File className="h-4 w-4" />;
    
    if (mimeType.includes('pdf')) return <FileText className="h-4 w-4 text-red-600" />;
    if (mimeType.includes('image')) return <File className="h-4 w-4 text-blue-600" />;
    if (mimeType.includes('word') || mimeType.includes('document')) {
      return <FileText className="h-4 w-4 text-blue-800" />;
    }
    return <File className="h-4 w-4" />;
  };

  const groupedDocuments = documents.reduce((acc, doc) => {
    if (!acc[doc.documentType]) {
      acc[doc.documentType] = [];
    }
    acc[doc.documentType].push(doc);
    return acc;
  }, {} as Record<SPVDocumentType, SPVDocument[]>);

  const requiredDocumentTypes: SPVDocumentType[] = [
    "CERTIFICATE_OF_INCORPORATION",
    "PAN_CARD",
    "GST_REGISTRATION",
    "BOARD_RESOLUTION",
    "BANK_PROOF",
    "AUTHORIZED_SIGNATORY_ID",
    "MOA_AOA",
  ];

  const verifiedCount = requiredDocumentTypes.filter(type => 
    groupedDocuments[type]?.some(doc => doc.verified)
  ).length;

  return (
    <div className="space-y-6">
      {/* Documents List */}
      <Card>
        <CardHeader>
          <CardTitle>Uploaded Documents</CardTitle>
          <CardDescription>
            {documents.length} document{documents.length !== 1 ? 's' : ''} uploaded
          </CardDescription>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No documents uploaded yet</p>
              {canUpload && (
                <p className="text-sm text-muted-foreground mt-2">
                  Upload documents to start the verification process
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(groupedDocuments).map(([type, docs]) => (
                <div key={type} className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    {documentTypeLabels[type as SPVDocumentType]}
                  </h4>
                  {docs.map(document => (
                    <div key={document.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        {getDocumentIcon(document.mimeType)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="text-sm font-medium truncate">{document.fileName}</p>
                          <Badge 
                            variant={document.verified ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {document.verified ? "Verified" : "Pending"}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{formatFileSize(document.fileSize)}</span>
                          <span>Uploaded {format(new Date(document.uploadedAt), 'MMM d, yyyy')}</span>
                          {document.uploader && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {document.uploader.name}
                            </span>
                          )}
                        </div>
                        
                        {document.notes && (
                          <p className="text-xs text-muted-foreground mt-1 italic">
                            "{document.notes}"
                          </p>
                        )}
                        
                        {document.verified && document.verifier && (
                          <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                            <Check className="h-3 w-3" />
                            Verified by {document.verifier.name} on{' '}
                            {format(new Date(document.verifiedAt!), 'MMM d, yyyy')}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {/* View/Download Button */}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(document.fileUrl, '_blank')}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        
                        {/* Verification Actions (Admin only) */}
                        {canVerify && !document.verified && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-green-600 border-green-600 hover:bg-green-50"
                              onClick={() => openVerificationDialog(document, 'verify')}
                            >
                              <Check className="h-4 w-4 mr-1" />
                              Verify
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 border-red-600 hover:bg-red-50"
                              onClick={() => openVerificationDialog(document, 'reject')}
                            >
                              <X className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document Status Tracking */}
      {canUpload && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Document Status
            </CardTitle>
            <CardDescription>
              Track required document uploads and verification status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {requiredDocumentTypes.map(type => {
                const docs = groupedDocuments[type] || [];
                const hasDocument = docs.length > 0;
                const isVerified = docs.some(doc => doc.verified);
                const isPending = hasDocument && !isVerified;

                return (
                  <div key={type} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {isVerified ? (
                          <Check className="h-5 w-5 text-green-600" />
                        ) : hasDocument ? (
                          <Clock className="h-5 w-5 text-yellow-600" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {documentTypeLabels[type]}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {isVerified ? 'Verified' : hasDocument ? 'Pending verification' : 'Not uploaded'}
                        </p>
                      </div>
                    </div>
                    {!hasDocument && onUploadDocument && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onUploadDocument(type)}
                        className="ml-2"
                      >
                        <Upload className="h-4 w-4 mr-1" />
                        Upload
                      </Button>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Pending Documents Reminder */}
            {(() => {
              const missingDocs = requiredDocumentTypes.filter(type => !groupedDocuments[type]?.length);
              const pendingDocs = requiredDocumentTypes.filter(type => {
                const docs = groupedDocuments[type] || [];
                return docs.length > 0 && !docs.some(doc => doc.verified);
              });

              if (missingDocs.length > 0 || pendingDocs.length > 0) {
                return (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-yellow-800">
                          Action Required
                        </h4>
                        {missingDocs.length > 0 && (
                          <p className="text-sm text-yellow-700 mt-1">
                            <strong>{missingDocs.length} document{missingDocs.length !== 1 ? 's' : ''} missing:</strong>{' '}
                            {missingDocs.map(type => documentTypeLabels[type]).join(', ')}
                          </p>
                        )}
                        {pendingDocs.length > 0 && (
                          <p className="text-sm text-yellow-700 mt-1">
                            <strong>{pendingDocs.length} document{pendingDocs.length !== 1 ? 's' : ''} pending verification:</strong>{' '}
                            {pendingDocs.map(type => documentTypeLabels[type]).join(', ')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}
          </CardContent>
        </Card>
      )}

      {/* Verification Dialog */}
      <Dialog 
        open={verificationDialog.isOpen} 
        onOpenChange={(open) => {
          if (!open) {
            setVerificationDialog({ isOpen: false, document: null, action: null });
            setVerificationNotes("");
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {verificationDialog.action === 'verify' ? 'Verify Document' : 'Reject Document'}
            </DialogTitle>
            <DialogDescription>
              {verificationDialog.action === 'verify' 
                ? 'Confirm that this document is valid and meets requirements'
                : 'Provide a reason for rejecting this document'
              }
            </DialogDescription>
          </DialogHeader>
          
          {verificationDialog.document && (
            <div className="space-y-4">
              <div className="p-3 bg-muted rounded-lg">
                <p className="font-medium">{verificationDialog.document.fileName}</p>
                <p className="text-sm text-muted-foreground">
                  {documentTypeLabels[verificationDialog.document.documentType]}
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="verificationNotes">
                  {verificationDialog.action === 'verify' ? 'Verification Notes (Optional)' : 'Rejection Reason *'}
                </Label>
                <Textarea
                  id="verificationNotes"
                  placeholder={
                    verificationDialog.action === 'verify' 
                      ? "Add any notes about the verification..."
                      : "Explain why this document is being rejected..."
                  }
                  value={verificationNotes}
                  onChange={(e) => setVerificationNotes(e.target.value)}
                  className="min-h-[80px]"
                />
              </div>
              
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setVerificationDialog({ isOpen: false, document: null, action: null })}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant={verificationDialog.action === 'verify' ? 'default' : 'destructive'}
                  onClick={() => handleVerificationAction(verificationDialog.action!)}
                  disabled={isSubmitting || (verificationDialog.action === 'reject' && !verificationNotes.trim())}
                >
                  {isSubmitting ? 'Processing...' : 
                   verificationDialog.action === 'verify' ? 'Verify Document' : 'Reject Document'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
